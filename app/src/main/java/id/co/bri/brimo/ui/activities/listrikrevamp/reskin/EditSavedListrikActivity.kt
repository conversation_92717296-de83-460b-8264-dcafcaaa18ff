package id.co.bri.brimo.ui.activities.listrikrevamp.reskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.IFormListrikReskinPresenter
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ISavedListrikReskinView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataException
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataResult
import id.co.bri.brimo.databinding.ActivityAddSavedListrikNsBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import javax.inject.Inject

class EditSavedListrikActivity: NewSkinBaseActivity(), IPulsaDataReskinView {
    private var _binding: ActivityAddSavedListrikNsBinding? = null
    protected val binding get() = _binding!!

    @Inject
    lateinit var presenter: IFormListrikReskinPresenter<ISavedListrikReskinView>

    companion object {
        private var savedResponse: MutableList<SavedResponse> = mutableListOf()
        private var historyResponse: MutableList<HistoryResponse> = mutableListOf()

        @JvmStatic
        fun launchIntent(caller: Activity,
                         savedResponses: MutableList<SavedResponse>,
                         historyResponses: MutableList<HistoryResponse>,
                         fromFastMenu: Boolean) {
            isFromFastMenu = fromFastMenu
            savedResponse = savedResponses
            historyResponse = historyResponses

            caller.apply {
                startActivityForResult(
                    Intent(
                        this,
                        EditSavedListrikActivity::class.java
                    ), Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityAddSavedListrikNsBinding.inflate(layoutInflater)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()
    }

    private fun onBindView() {
    }

    private fun injectDependency() {
        activityComponent.inject(this)

//        presenter.apply {
//            view = this@EditSavedListrikActivity
//            start()
//        }
    }

    private fun onBindIntentData() {
    }

    override fun onSuccess(res: PulsaDataResult) {

    }

    override fun onExceptionReskin(exception: PulsaDataException) {
    }

    override fun onSuccessAccountList(
        accountList: MutableList<AccountModel>,
        mainAccount: AccountModel
    ) {
    }
}