package id.co.bri.brimo.ui.activities.pulsadata.reskin

import android.app.Activity
import android.content.Intent
import android.text.InputType
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.transition.Fade
import androidx.transition.Slide
import androidx.transition.TransitionManager
import androidx.transition.TransitionSet
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataException
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataResult
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaErrorCode
import id.co.bri.brimo.databinding.ActivityOpenBillConfirmationReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.reskin.ReceiptType
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.request.FastPaymentOpenRevampRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.pulsarevamp.reskin.PaymentNS
import id.co.bri.brimo.ui.activities.base.BaseTransactionActivity
import id.co.bri.brimo.ui.activities.transaction_process.TransactionProcessActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.ui.fragments.pin.reskin.PinReskinFragment
import javax.inject.Inject

class ConfirmationPulsaDataActivity: BaseTransactionActivity<ActivityOpenBillConfirmationReskinBinding>(
    ActivityOpenBillConfirmationReskinBinding::inflate),
    PinReskinFragment.SendPin,
//    PinFragment.SendPin,
    IPulsaDataReskinView {
    private var isCompletedPin = false
    private var pin = ""

    @Inject
    lateinit var presenter: IPulsaDataReskinPresenter<IPulsaDataReskinView>

    companion object {
        const val TAG = "ConfirmationPulsaDataActivity"
        private var dataConfirm: GeneralConfirmationResponse?= null
        private var dataSaved: MutableList<SavedResponse> = mutableListOf()

        private var dataAccount: AccountModel?= null

        private const val ANIMATE_DUR: Long = 300

        @JvmStatic
        fun launchIntent(
            caller: Activity, fromFastMenu: Boolean,
            response: GeneralConfirmationResponse, sumberDana: AccountModel,
            savedList: MutableList<SavedResponse>
        ) {
            dataConfirm = response
            dataAccount = sumberDana
            isFromFastMenu = fromFastMenu
            dataSaved = savedList

            Intent(caller, ConfirmationPulsaDataActivity::class.java).let { intent ->
                caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
            }
        }
    }

    override fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@ConfirmationPulsaDataActivity
            start()
        }
    }

    override fun onBindView() {
        GeneralHelper.setToolbarNs(this, binding.toolbar, "Konfirmasi")

        binding.btnLihatLebih.visibility = View.GONE
        binding.btnSubmit.setOnItemClickListener {
            val pinFragment = PinReskinFragment(this@ConfirmationPulsaDataActivity, this)
            pinFragment.show()
        }
        binding.switchSave.setOnCheckedChangeListener { _, isChecked ->
            val transitionSet: TransitionSet = TransitionSet()
                .addTransition(Fade())
                .addTransition(Slide(Gravity.LEFT))
                .setDuration(ANIMATE_DUR)

            TransitionManager.beginDelayedTransition(binding.bivSaveName.parent as ViewGroup, transitionSet)

            if(isChecked) {
                binding.bivSaveName.visibility = View.VISIBLE
            } else {
                binding.bivSaveName.visibility = View.GONE
            }
        }

        initViews()
    }

    private fun initViews() {
        val billingDetail = dataConfirm!!.billingDetail
        val mSaved = dataSaved.find { it.subtitle == billingDetail.description }
        binding.rlFavorit.visibility = if(mSaved!=null || isFromFastMenu) View.GONE else View.VISIBLE
        if(!isFromFastMenu) binding.llSavedAs.visibility = if(mSaved!=null) View.VISIBLE else View.GONE
        binding.tvSavedAs.text = String.format(
            GeneralHelper.getString(R.string.txt_tersimpan_sebagai),
            mSaved?.title
        )

        GeneralHelper.loadIconTransaction(
            this,
            billingDetail.iconPath,
            billingDetail.iconName.split("\\.".toRegex())
                .dropLastWhile { it.isEmpty() }.toTypedArray().get(0),
            binding.ivArea,
            GeneralHelper.getImageId(this, "bri")
        )

        binding.tvNameCust.text = billingDetail.subtitle
        binding.tvNumberCust.text = String.format(
            GeneralHelper.getString(R.string.transaction_detail_content),
            billingDetail.title,
            billingDetail.description
        )

        binding.tvNominal.text = dataConfirm!!.amountString
        binding.tvAdminFee.text = dataConfirm!!.adminFeeString

        binding.bivSaveName.apply {
            setInputType(InputType.TYPE_CLASS_TEXT)
            addEndIcon(R.drawable.ic_clear_ns) {
                clearText()
            }
        }

        binding.btnSubmit.setAmountText(dataConfirm!!.payAmountString)
        binding.totalTagihan.text = dataConfirm!!.payAmountString

        dataAccount?.let {
            binding.tvNumberAccount.text = it.name
            binding.tvNominalAccount.text = it.acoountString

            GeneralHelper.loadIconTransaction(
                this,
                it.imagePath,
                it.imageName,
                binding.ivRekening,
                R.drawable.img_card_bg)
        }
    }

    override fun onSendPinComplete(pin: String) {
        isCompletedPin = true
        this.pin = pin
        val param = PaymentNS(
            dataConfirm!!.referenceNumber,
            pin!!,
            dataAccount?.acoount!!,
            dataConfirm!!.pfmCategory.toString(),
            binding.bivSaveName.getText()
        )
        showProgress()

        presenter.executeRequest(
            url = if(isFromFastMenu)
                GeneralHelper.getString(R.string.url_payment_pulsa_data_fm_revamp)
            else "PjBhVJD2oNh6zzQCIznEbX7TmHgDoFnuO92DaIukU4M=",
            requestParam = if(isFromFastMenu) FastPaymentOpenRevampRequest(
                presenter.getFastMenuRequest(),
                param.referenceNumber,
                param.pin,
                param.accountNumber,
                param.pfmCategory.toString(),
            ) else param,
            responseType = ReceiptRevampResponse::class.java,
        )
    }

    override fun onLupaPin() {
    }

    fun mParameterModel(): ParameterModel {
        val parameterModel = ParameterModel()
        parameterModel.stringLabelTujuan = GeneralHelper.getString(R.string.nomor_tujuan)
        parameterModel.stringLabelNominal = GeneralHelper.getString(R.string.nominal)
        parameterModel.defaultIcon = R.drawable.ic_default_pulsa
        return parameterModel
    }

    override fun onSuccess(res: PulsaDataResult) {
        when (res) {
            is PulsaDataResult.Receipt -> {
                val receiptRevampResponse = res.data

                finish()
                dataAccount?.let {
                    TransactionProcessActivity.launchIntent(this,
                        isFromFastMenu, data = receiptRevampResponse, it, ReceiptType.OTHER
                    )
                }
            }
            else -> Unit
        }
    }

    override fun onExceptionReskin(exception: PulsaDataException) {
        when (exception) {
            is PulsaDataException.KnownError -> handlingError(exception.code)
            is PulsaDataException.UnknownError -> {
                OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                    supportFragmentManager,
                    R.drawable.ic_sad_illustration,
                    "ic_sad_illustration",
                    "Terjadi Kesalahan",
                    "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
                    btnFirstFunction = {
                        showGeneralErrorDialog {
                            showProgress()
                            val param = PaymentNS(
                                dataConfirm!!.referenceNumber,
                                pin,
                                dataAccount?.acoount!!,
                                dataConfirm!!.pfmCategory.toString(),
                                binding.bivSaveName.getText()
                            )

                            presenter.executeRequest(
                                url = if(isFromFastMenu)
                                    GeneralHelper.getString(R.string.url_payment_pulsa_data_fm_revamp)
                                else "PjBhVJD2oNh6zzQCIznEbX7TmHgDoFnuO92DaIukU4M=",
                                requestParam = if(isFromFastMenu) FastPaymentOpenRevampRequest(
                                    presenter.getFastMenuRequest(),
                                    param.referenceNumber,
                                    param.pin,
                                    param.accountNumber,
                                    param.pfmCategory.toString(),
                                ) else param,
                                responseType = ReceiptRevampResponse::class.java,
                            )
                        }
                    },
                    btnSecondFunction = {

                    },
                    false,
                    firstBtnTxt = "Coba Lagi",
                    secondBtnTxt = "Tutup",
                    false,
                    showCloseButton = true,
                    showPill = true
                )
            }
        }
    }

    private fun showGeneralErrorDialog(
        title: String = "Terjadi Kesalahan",
        message: String = "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
        imgName: String = "ic_sad_illustration",
        onRetry: (() -> Unit)? = null
    ) {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_sad_illustration,
            imgName,
            title,
            message,
            btnFirstFunction = { onRetry?.invoke() },
            btnSecondFunction = { /* optional */ },
            false,
            firstBtnTxt = getString(R.string.retry),
            secondBtnTxt = getString(R.string.close),
            false,
            showCloseButton = true,
            showPill = true
        )
    }

    private fun handlingError(code: String) {
        println("handlingError: $code")
        when (code) {
            PulsaErrorCode.EXCEPTION_93.code -> {
                showGeneralErrorDialog()
            }
            PulsaErrorCode.EXCEPTION_12.code -> {
                showProgress()
                showGeneralErrorDialog {
                    val param = PaymentNS(
                        dataConfirm!!.referenceNumber,
                        pin,
                        dataAccount?.acoount!!,
                        dataConfirm!!.pfmCategory.toString(),
                        binding.bivSaveName.getText()
                    )

                    presenter.executeRequest(
                        url = if(isFromFastMenu)
                            GeneralHelper.getString(R.string.url_payment_pulsa_data_fm_revamp)
                        else "PjBhVJD2oNh6zzQCIznEbX7TmHgDoFnuO92DaIukU4M=",
                        requestParam = if(isFromFastMenu) FastPaymentOpenRevampRequest(
                            presenter.getFastMenuRequest(),
                            param.referenceNumber,
                            param.pin,
                            param.accountNumber,
                            param.pfmCategory.toString(),
                        ) else param,
                        responseType = ReceiptRevampResponse::class.java,
                    )
                }
//                showSnackbar("Terjadi gangguan sistem. Silakan coba lagi, ya.", ALERT_ERROR)
            }
        }
    }
}