/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#ifdef _WIN32

#include <intrin.h>
#include <stdint.h>

#include <folly/Portability.h>

FOLLY_ALWAYS_INLINE
int64_t __sync_fetch_and_add(volatile int64_t* ptr, int64_t value) {
  return _InterlockedExchangeAdd64(ptr, value);
}

#endif
