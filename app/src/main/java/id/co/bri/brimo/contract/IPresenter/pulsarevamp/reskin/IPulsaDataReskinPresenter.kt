package id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin

import id.co.bri.brimo.contract.IPresenter.base.IBaseTransactionPresenter
import id.co.bri.brimo.contract.IView.base.IBaseTransactionView
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType

interface IPulsaDataReskinPresenter<V : IBaseTransactionView> : IBaseTransactionPresenter<V> {
    fun <Req, Res> executeRequest(
        url: String,
        requestParam: Req?,
        responseType: Class<Res>,
        isSaved: FavoriteType?= null
    )
}