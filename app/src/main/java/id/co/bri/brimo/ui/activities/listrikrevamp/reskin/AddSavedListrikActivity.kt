package id.co.bri.brimo.ui.activities.listrikrevamp.reskin

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.IFormListrikReskinPresenter
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ISavedListrikReskinView
import id.co.bri.brimo.databinding.ActivityAddSavedListrikNsBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.PlnListResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.ui.activities.ScannerActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.listrik.BSFragmentListrikOption
import id.co.bri.brimo.util.RxBus
import javax.inject.Inject

class AddSavedListrikActivity: NewSkinBaseActivity(), ISavedListrikReskinView {
    private var _binding: ActivityAddSavedListrikNsBinding? = null
    protected val binding get() = _binding!!

    @Inject
    lateinit var presenter: IFormListrikReskinPresenter<ISavedListrikReskinView>

    private var isErrorAfterSubmit = false
    private var firstTimeSelectOption = false

    private val plnList: MutableList<PlnListResponse> = mutableListOf()
    private val savedList: MutableList<SavedResponse> = mutableListOf()

    protected var activityTextListener: TextWatcher = object : TextWatcher {
        override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            // Override in child class if needed
        }

        override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            changeText(charSequence, i, i1, i2)
        }

        override fun afterTextChanged(editable: Editable) {
            // Override in child class if needed
        }
    }

    companion object {
        const val TAG = "AddSavedListrikActivity"

        const val INPUT_MAX_LENGTH = 20
        private var currentOption: PlnListResponse? = null

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean,
                         formList: MutableList<PlnListResponse>, savedList: MutableList<SavedResponse>) {
            isFromFastMenu = fromFastMenu
            caller.apply {
                startActivityForResult(
                    Intent(
                        this,
                        AddSavedListrikActivity::class.java
                    ).apply {
                        putExtras(Bundle().apply {
                            putParcelableArrayList(TAG, ArrayList(formList))
                            putParcelableArrayList("$TAG.savedList", ArrayList(savedList))
                        })
                    }, Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityAddSavedListrikNsBinding.inflate(layoutInflater)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()
    }

    private fun onBindView() {
        binding.etJenisListrik.setOnClickListener { showOptionListrik() }
        binding.llListrikType.setOnClickListener {
            showOptionListrik()
        }

        binding.bivNoPelanggan.apply {
            maxLength(INPUT_MAX_LENGTH)
            setInputType(InputType.TYPE_CLASS_TEXT)
            addTextChangedListener(activityTextListener)
            addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24, marginDp = 5) {
                clearText()
            }
            addEndIcon(R.drawable.ic_scan_reskin) {
                checkPermissionAndOpenScanner()
            }
        }

        binding.btnSubmit.setOnClickListener {
            binding.bivNoPelanggan.clearError()
            binding.etSavedName.clearError()

            val noPel = binding.bivNoPelanggan.getText()
            val savedName = binding.etSavedName.getText()

            if(noPel.isNotEmpty() && savedName.isNotEmpty() && currentOption != null) {
                val availableNoPel = savedList.any { it.description == noPel }
                val availableName = savedList.any { it.title == savedName }

                if(availableName || availableNoPel){
                    if(availableName) binding.bivNoPelanggan.setError("Nama sudah terdaftar. Coba nama lain, yuk.")
                } else {
                    presenter.addSavedList(param = SavedListNs(
                        paymentNumber = noPel,
                        plnTypeCode = currentOption?.code,
                    ), binding.etSavedName.getText())
                }
            }
        }

        binding.bslContent.setTextToolbar(this@AddSavedListrikActivity, "Tambah Daftar Favorit")
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@AddSavedListrikActivity
            start()
        }
    }

    private fun showOptionListrik() {
        plnList.removeIf { it.code == "nontaglis" }

        BSFragmentListrikOption(plnList) { _, item ->
            currentOption = item

            if(firstTimeSelectOption){
                isErrorAfterSubmit = !isValidationNoMeter()
            }

            binding.etJenisListrik.setText(currentOption?.name)

            //load icon transaction
            GeneralHelper.loadIconTransaction(
                this@AddSavedListrikActivity,
                currentOption?.iconPath,
                currentOption?.iconName!!.split("\\.".toRegex())
                    .dropLastWhile { it.isEmpty() }
                    .toTypedArray()[0],
                binding.ivArea,
                GeneralHelper.getImageId(this@AddSavedListrikActivity, "bri")
            )
            binding.iconContainer.visibility = View.VISIBLE
            binding.iconContainer.setBackgroundResource(R.drawable.bg_white_full_rounded_ns)
            binding.regionTextview.visibility = View.VISIBLE
            binding.ivArea.setPadding(0, 0, 0, 0)
        }.apply {
            show(supportFragmentManager, BSFragmentListrikOption.TAG)
        }
    }

    override fun changeText(charSequence: CharSequence?, i: Int, i1: Int, i2: Int) {
        val isNotEmpty = binding.etJenisListrik.length() != 0

        if(isErrorAfterSubmit) {
            if(isValidationNoMeter()) {
                binding.bivNoPelanggan.clearError()
                isErrorAfterSubmit = false
            }
        }

        if(isNotEmpty) {
            binding.btnSubmit.backgroundTintList = ColorStateList.valueOf(
                GeneralHelper.getColor(R.color.primary_default_ns)
            )
        } else {
            binding.btnSubmit.backgroundTintList = ColorStateList.valueOf(
                GeneralHelper.getColor(R.color.black_ns_300)
            )
        }
    }

    private fun checkPermissionAndOpenScanner() {
        val permissionCheck = ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
        if (permissionCheck != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.CAMERA),
                Constant.REQUEST_CAMERA
            )
        } else {
            // Permission already granted
//            ScannerReskinActivity.launchIntent(this, isFromFastMenu)
            ScannerActivity.launchIntentScanner(this)
        }
    }

    fun isValidationNoMeter(): Boolean {
        val minLength = when (currentOption?.code) {
            "prepaid" -> 11
            "postpaid" -> 12
            "nontaglis" -> 13
            else -> throw RuntimeException("Diluar jenis listrik")
        }

        return binding.bivNoPelanggan.getText().length >= minLength &&
                binding.etJenisListrik.length() != 0
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == Constant.REQUEST_CAMERA) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // Permission granted, now open the scanner
                ScannerActivity.launchIntentScanner(this)
//                ScannerReskinActivity.launchIntent(this, isFromFastMenu)
            } else {
                // Permission denied
                showAlertFinish(getString(R.string.notes_need_permission))
            }
        }
    }

    private fun onBindIntentData() {
        intent?.let{
            it.getParcelableArrayListExtra<PlnListResponse>(TAG)?.let {
                    elements -> plnList.addAll(elements)
            }
            it.getParcelableArrayListExtra<SavedResponse>("$TAG.savedList")?.let {
                    elements -> savedList.addAll(elements)
            }

//            "$TAG.savedList"
        }

        showOptionListrik()
    }

    override fun onSuccess(data: RestResponse) {
        RxBus.send(FavoriteEvent(isAddFavorite = true))
        finish()
    }
}