package id.co.bri.brimo.ui.activities.listrikrevamp.reskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HistoryAdapterNs
import id.co.bri.brimo.adapters.SavedAdapterNs
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.IFormListrikReskinPresenter
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ISavedListrikReskinView
import id.co.bri.brimo.databinding.ActivitySearchSavedHistoryBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import javax.inject.Inject

class SearchSavedHistoryListrikActivity: NewSkinBaseActivity(), SavedAdapterNs.ClickItem, HistoryAdapterNs.ClickItem, ISavedListrikReskinView {
    private var _binding: ActivitySearchSavedHistoryBinding? = null
    protected val binding get() = _binding!!

    private lateinit var savedAdapter: SavedAdapterNs
    private lateinit var historyAdapter: HistoryAdapterNs
    private var filteredSavedResponses = mutableListOf<SavedResponse>()
    private var filteredHistoryResponses = mutableListOf<HistoryResponse>()
    private var defaultIcon = 0
    private var lastClickTime = 0L
    private var currentSearchQuery = ""

    @Inject
    lateinit var presenter: IFormListrikReskinPresenter<ISavedListrikReskinView>

    companion object Companion {
        private var savedResponse: MutableList<SavedResponse> = mutableListOf()
        private var historyResponse: MutableList<HistoryResponse> = mutableListOf()

        @JvmStatic
        fun launchIntent(caller: Activity,
                         savedResponses: MutableList<SavedResponse>,
                         historyResponses: MutableList<HistoryResponse>,
                         fromFastMenu: Boolean) {
            isFromFastMenu = fromFastMenu
            savedResponse = savedResponses
            historyResponse = historyResponses

            caller.apply {
                startActivityForResult(
                    Intent(
                        this,
                        SearchSavedHistoryListrikActivity::class.java
                    ), Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivitySearchSavedHistoryBinding.inflate(layoutInflater)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()
    }

    private fun onBindView() {
        GeneralHelperNewSkin.setToolbar(
            this,binding.toolbar.toolbar,
            "Listrik",
        )

        // Initialize filtered lists as empty initially (blank search result)
        filteredSavedResponses.clear()
        filteredHistoryResponses.clear()

        updateEmptyState()
        setupAdapters()
        setupSearchView()
    }

    private fun setupAdapters() {
        // Saved adapter
        initiateSavedAdapter()

        // History adapter
        initiateHistoryAdapter()
    }

    fun initiateHistoryAdapter() {
        binding.rvRiwayat.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        historyAdapter =
            HistoryAdapterNs(
                this,
                historyResponse,
                this,
                0,
                BaseActivity.isFromFastMenu,
                "listrik",
            )
        binding.rvRiwayat.adapter = historyAdapter
    }

    fun initiateSavedAdapter() {
        binding.rvDaftarFavorit.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        savedAdapter =
            SavedAdapterNs(this, savedResponse, this, 0, BaseActivity.isFromFastMenu)
        binding.rvDaftarFavorit.adapter = savedAdapter
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@SearchSavedHistoryListrikActivity
            start()
        }
    }

    private fun setupSearchView() {
        // Request focus on SearchView when activity starts
        binding.searchView.post {
            binding.searchView.requestFocus()
        }

        // Handle focus change to manually update background
        binding.searchView.setOnQueryTextFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                binding.searchView.setBackgroundResource(R.drawable.bg_input_double_border_focused_ns)
            } else {
                binding.searchView.setBackgroundResource(R.drawable.bg_input_black_100_brimo_ns)
            }
        }

        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                currentSearchQuery = newText ?: ""
                filterData(currentSearchQuery)
                return false
            }
        })
    }

    private fun filterData(query: String) {
        val lowerCaseQuery = query.lowercase()

        // Filter saved responses
        filteredSavedResponses.clear()
        if (query.isNotEmpty()) {
            savedResponse.forEach { saved ->
                if (saved.title?.lowercase()?.contains(lowerCaseQuery) == true) {
                    filteredSavedResponses.add(saved)
                }
            }
        }

        // Filter history responses
        filteredHistoryResponses.clear()
        if (query.isNotEmpty()) {
            historyResponse.forEach { history ->
                if (history.title?.lowercase()?.contains(lowerCaseQuery) == true) {
                    filteredHistoryResponses.add(history)
                }
            }
        }

        // Update adapters with search query for highlighting
        savedAdapter.setSearchQuery(query)
        historyAdapter.setSearchQuery(query)

        // Notify adapters
        savedAdapter.notifyDataSetChanged()
        historyAdapter.notifyDataSetChanged()

        // Update empty state
        updateEmptyState(query.isNotEmpty())
    }

    private fun updateEmptyState(isSearching: Boolean = false) {
        // Show/hide sections based on content and search state
        binding.contentFavorit.visibility = if (filteredSavedResponses.isNotEmpty()) View.VISIBLE else View.GONE
        binding.contentRiwayat.visibility = if (filteredHistoryResponses.isNotEmpty()) View.VISIBLE else View.GONE

        // Show/hide start search view based on whether user is searching
        if (isSearching) {
            // User has entered some characters, hide start search view
            binding.llStartSearch.visibility = View.GONE

            // Show no search results if searching and both lists are empty
            val hasAnyResults = filteredSavedResponses.isNotEmpty() || filteredHistoryResponses.isNotEmpty()
            if (!hasAnyResults) {
                binding.llNoDataSearchFound.visibility = View.VISIBLE
            } else {
                binding.llNoDataSearchFound.visibility = View.GONE
            }
        } else {
            // No query entered, show start search view and hide no data found
            binding.llStartSearch.visibility = View.VISIBLE
            binding.llNoDataSearchFound.visibility = View.GONE
        }
    }

    private fun updateSavedItemInLists(data: SavedResponse, position: Int, updateAction: (SavedResponse) -> Unit) {
        // Find and update item in original list
        val originalIndex = savedResponse.indexOfFirst { it.value == data.value }
        if (originalIndex != -1) {
            updateAction(savedResponse[originalIndex])
        }

        // Find and update item in filtered list
        val filteredIndex = filteredSavedResponses.indexOfFirst { it.value == data.value }
        if (filteredIndex != -1) {
            updateAction(filteredSavedResponses[filteredIndex])
            savedAdapter.notifyItemChanged(filteredIndex)
        }
    }

    private fun onBindIntentData() {
    }

    override fun onClickSavedItem(savedResponse: SavedResponse?) {

    }

    override fun onClickUpdateItem(
        savedResponse: SavedResponse?,
        position: Int
    ) {
    }

    override fun onClickHistoryItem(historyResponse: HistoryResponse?) {
    }

    override fun onSuccess(data: RestResponse) {

    }
}