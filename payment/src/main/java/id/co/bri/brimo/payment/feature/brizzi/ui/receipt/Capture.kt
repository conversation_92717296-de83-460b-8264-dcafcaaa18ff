package id.co.bri.brimo.payment.feature.brizzi.ui.receipt

import android.graphics.Bitmap
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.activity.ComponentActivity
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.core.graphics.createBitmap
import androidx.core.graphics.set
import androidx.core.view.drawToBitmap
import com.google.zxing.BarcodeFormat
import com.google.zxing.MultiFormatWriter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.math.roundToInt

suspend fun composableToBitmap(
    mainScope: CoroutineScope,
    activity: ComponentActivity,
    width: Dp,
    screenDensity: Density,
    content: @Composable () -> Unit
): Bitmap = suspendCancellableCoroutine { continuation ->
    mainScope.launch {
        val contentWidthInPixels = (screenDensity.density * width.value).roundToInt()

        val composeViewContainer = FrameLayout(activity).apply {
            layoutParams =
                ViewGroup.LayoutParams(contentWidthInPixels, ViewGroup.LayoutParams.WRAP_CONTENT)
            visibility = View.INVISIBLE
        }

        val composeView = ComposeView(activity).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnDetachedFromWindow)
            setContent { content() }
        }

        composeViewContainer.addView(composeView)

        val decorView = activity.window.decorView as ViewGroup
        decorView.addView(composeViewContainer)

        Handler(Looper.getMainLooper()).post {
            composeViewContainer.measure(
                View.MeasureSpec.makeMeasureSpec(contentWidthInPixels, View.MeasureSpec.EXACTLY),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )

            val contentHeightInPixels = composeViewContainer.measuredHeight

            composeViewContainer.layout(
                0,
                0,
                contentWidthInPixels,
                contentHeightInPixels
            )

            val bitmap = composeView.drawToBitmap(config = Bitmap.Config.ARGB_8888)
            continuation.resume(bitmap)

            decorView.removeView(composeViewContainer)
        }
    }
}

fun generateQrCode(
    content: String,
    size: Int
): Bitmap {
    val writer = MultiFormatWriter()
    val bitMatrix = writer.encode(
        content,
        BarcodeFormat.QR_CODE,
        size,
        size,
    )

    val bitmap = createBitmap(size, size, Bitmap.Config.ARGB_4444)

    for (x in 0 until size) {
        for (y in 0 until size) {
            bitmap[x, y] = if (bitMatrix[x, y]) Color.BLACK else Color.WHITE
        }
    }

    return bitmap
}

fun generateBarCode(
    content: String,
    width: Int,
    height: Int
): Bitmap {
    val writer = MultiFormatWriter()
    val bitMatrix = writer.encode(
        content,
        BarcodeFormat.CODE_128,
        width,
        height,
    )

    val bitmap = createBitmap(width, height, Bitmap.Config.ARGB_4444)

    for (x in 0 until width) {
        for (y in 0 until height) {
            bitmap[x, y] = if (bitMatrix[x, y]) Color.BLACK else Color.WHITE
        }
    }

    return bitmap
}
