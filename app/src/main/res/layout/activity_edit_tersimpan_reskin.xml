<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:fitsSystemWindows="true">


	<include
			android:id="@+id/toolbar"
			layout="@layout/toolbar_brimo_ns" />

	<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:layout_marginTop="70dp"
			android:orientation="vertical">

		<ScrollView
				android:id="@+id/content"
				android:layout_width="match_parent"
				android:layout_height="0dp"
				android:layout_weight="1"
				android:paddingHorizontal="16dp"
				android:background="@drawable/bg_card_rounded_ns"
				android:clipToPadding="false">

			<LinearLayout
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:orientation="vertical"
					android:paddingTop="16dp">

				<LinearLayout
						android:id="@+id/et_wallet"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:orientation="horizontal"
						android:gravity="center_vertical|center_horizontal"
						android:layout_gravity="center_horizontal"
						android:minHeight="64dp"
						android:paddingHorizontal="21dp"
						android:background="@drawable/selector_input_field_reskin">

					<RelativeLayout
							android:id="@+id/rl_inisial_open"
							android:layout_width="@dimen/space_x5"
							android:layout_height="@dimen/space_x5"
							android:layout_gravity="center_vertical"
							android:background="@drawable/round_button"
							android:backgroundTint="@color/accent3Color"
							android:orientation="vertical"
							android:visibility="visible">

						<ImageView
								android:id="@+id/iv_inisial_open"
								android:layout_width="match_parent"
								android:layout_height="match_parent"
								android:layout_gravity="center" />

						<TextView
								android:id="@+id/tv_inisial_open"
								style="@style/BodySmallText.DemiBold.BlueLight"
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:layout_centerInParent="true"
								android:gravity="center"
								android:text="@string/empty" />

					</RelativeLayout>

					<LinearLayout
							android:id="@+id/ll_logo_open"
							android:layout_width="@dimen/space_x5"
							android:layout_height="@dimen/space_x5"
							android:layout_gravity="center_vertical"
							android:background="@drawable/round_history"
							android:orientation="vertical"
							android:visibility="gone">

						<ImageView
								android:id="@+id/iv_icon_open"
								android:layout_width="match_parent"
								android:layout_height="match_parent"
								android:layout_gravity="center"
								android:clickable="true" />
					</LinearLayout>


					<LinearLayout
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:orientation="vertical"
							tools:ignore="RtlSymmetry">

						<TextView
								android:id="@+id/regionTextview"
								style="@style/BodySmallText.Medium.Grey"
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:textColor="@color/text_disabled"
								android:paddingLeft="16dp"
								android:textSize="@dimen/micro_text"
								android:text="@string/region"
								android:visibility="visible" />

						<com.google.android.material.textfield.TextInputLayout
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								app:hintEnabled="false"
								android:gravity="center_vertical"
								app:boxStrokeWidth="0dp">

							<EditText
									android:id="@+id/etWilayah"
									android:layout_width="match_parent"
									android:layout_height="wrap_content"
									android:hint="@string/region"
									android:focusable="false"
									android:clickable="false"
									android:enabled="false"
									android:textStyle="bold"
									android:textColorHint="@color/text_black_default_ns"
									android:layout_gravity="center_vertical"
									android:background="@android:color/transparent"
									android:paddingRight="10dp"
									android:inputType="none|textNoSuggestions"
									android:paddingTop="4dp"
									android:paddingBottom="0dp"/>
						</com.google.android.material.textfield.TextInputLayout>
					</LinearLayout>


				</LinearLayout>

				<id.co.bri.brimo.ui.widget.input_til.BaseInputView
						android:id="@+id/lblTujuan"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:layout_marginTop="21dp"
						android:enabled="false"
						android:focusable="false"
						android:clickable="false"
						app:hintText="@string/nomor_pelanggan" />

				<id.co.bri.brimo.ui.widget.input_til.BaseInputView
						android:id="@+id/etSavedName"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:layout_marginTop="21dp"
						app:hintText="@string/nama_tersimpan" />


			</LinearLayout>

		</ScrollView>

		<LinearLayout
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:orientation="vertical"
				android:padding="16dp"
				android:elevation="8dp"
				android:background="@android:color/white">

			<Button
					android:id="@+id/btnSubmit"
					android:layout_width="match_parent"
					android:layout_height="56dp"
					style="@style/CustomButtonStyle"
					android:text="Simpan"
					android:textSize="16sp"
					android:textAllCaps="false"
					android:background="@drawable/rounded_button_ns"
					android:textColor="@color/selector_text_color_button_primary_ns"
					android:enabled="false" />
		</LinearLayout>

	</LinearLayout>
</FrameLayout>