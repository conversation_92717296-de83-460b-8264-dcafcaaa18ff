package id.co.bri.brimo.ui.activities.dompetdigitalreskin

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.InputType
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import android.widget.TextView
import androidx.transition.Fade
import androidx.transition.Slide
import androidx.transition.TransitionManager
import androidx.transition.TransitionSet
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin.IConfirmationDompetDigitalReskinPresenter
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.IConfirmationDompetDigitalReskinView
import id.co.bri.brimo.databinding.ActivityOpenBillConfirmationReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.reskin.ReceiptType
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.BillingDetailOpen
import id.co.bri.brimo.models.ParameterKonfirmasiModel
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.onExceptionWH
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.LupaPinFastActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.transaction_process.TransactionProcessActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.ui.fragments.pin.reskin.PinReskinFragment
import javax.inject.Inject

class ConfirmationDompetDigitalReskinActivity : NewSkinBaseActivity(),
    IConfirmationDompetDigitalReskinView, PinReskinFragment.SendPin {

    private lateinit var binding: ActivityOpenBillConfirmationReskinBinding
    private var isCompletedPin = false
    private var isExpanded = false
    private var pin: String = ""
    private val detailItems = mutableListOf<Pair<String, String>>()

    @Inject
    lateinit var presenter: IConfirmationDompetDigitalReskinPresenter<IConfirmationDompetDigitalReskinView>

    companion object {
        private lateinit var mOpenModel: BillingDetailOpen
        private lateinit var mAccountModel: AccountModel
        private var mSaved: String = ""
        private var mRefNum: String = ""
        private var mNote: String = ""
        private var mNominal: String = ""
        private var mNominalString: String = ""
        private var mAdmin: String = ""
        private var mAdminString: String = ""
        private var mUrlConfirm: String = ""
        private var mUrlPayment: String = ""
        private const val ANIMATE_DUR: Long = 300
        private const val SAVED_AS_TEXT_SIZE = 14f

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            openModel: BillingDetailOpen,
            saved: String,
            refNum: String,
            account: AccountModel,
            note: String,
            nominal: String,
            nominalString: String,
            admin: String,
            adminString: String,
            urlConfirm: String,
            urlPayment: String,
            fromFastMenu: Boolean
        ) {
            val intent = Intent(caller, ConfirmationDompetDigitalReskinActivity::class.java)
            mOpenModel = openModel
            mSaved = saved
            mRefNum = refNum
            mAccountModel = account
            mNote = note
            mNominal = nominal
            mNominalString = nominalString
            mAdmin = admin
            mAdminString = adminString
            mUrlConfirm = urlConfirm
            mUrlPayment = urlPayment
            isFromFastMenu = fromFastMenu
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }

        // Expand animation
        private fun expand(view: View) {
            view.measure(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            val targetHeight = view.measuredHeight

            view.layoutParams.height = 0
            view.visibility = View.VISIBLE

            val animator = ValueAnimator.ofInt(0, targetHeight).apply {
                duration = ANIMATE_DUR
                interpolator = DecelerateInterpolator()
                addUpdateListener { animation ->
                    view.layoutParams.height = animation.animatedValue as Int
                    view.requestLayout()
                }
            }
            animator.start()
        }

        // Collapse animation
        private fun collapse(view: View) {
            val initialHeight = view.measuredHeight

            val animator = ValueAnimator.ofInt(initialHeight, 0).apply {
                duration = ANIMATE_DUR
                interpolator = DecelerateInterpolator()
                addUpdateListener { animation ->
                    val value = animation.animatedValue as Int
                    view.layoutParams.height = value
                    view.requestLayout()
                    if (value == 0) {
                        view.visibility = View.GONE
                    }
                }
            }
            animator.start()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOpenBillConfirmationReskinBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlConfirm(mUrlConfirm)
        presenter.setUrlPayment(mUrlPayment)
        presenter.isGeneral(true)
        presenter.start()
    }

    private fun setupView() {
        GeneralHelperNewSkin.setToolbar(
            this, binding.toolbar,
            GeneralHelper.getString(R.string.str_konfirmasi)
        )

        binding.btnSubmit.setOnItemClickListener {
            val pinFragment =
                PinReskinFragment(this@ConfirmationDompetDigitalReskinActivity, this)
            pinFragment.show()
        }

        binding.switchSave.setOnCheckedChangeListener { _, isChecked ->
            val transitionSet: TransitionSet = TransitionSet()
                .addTransition(Fade())
                .addTransition(Slide(Gravity.LEFT))
                .setDuration(ANIMATE_DUR)

            TransitionManager.beginDelayedTransition(
                binding.bivSaveName.parent as ViewGroup,
                transitionSet
            )

            if (isChecked) {
                binding.bivSaveName.visibility = View.VISIBLE
            } else {
                binding.bivSaveName.visibility = View.GONE
            }
        }

        binding.btnLihatLebih.visibility = View.GONE

        if (isFromFastMenu) {
            binding.rlFavorit.visibility = View.GONE
        } else {
            if (mSaved.isNotEmpty()) {
                binding.rlFavorit.visibility = View.GONE
                binding.llSavedAs.visibility = View.VISIBLE
                binding.tvSavedAs.setText(
                    String.format(
                        GeneralHelper.getString(R.string.txt_tersimpan_sebagai),
                        mSaved
                    )
                )
            } else {
                binding.rlFavorit.visibility = View.VISIBLE
            }
        }

// Add from mOpenModel's title, subtitle, and description
        detailItems.add("Nomor E-Wallet" to mOpenModel.description)
        detailItems.add("Jenis E-Wallet" to mOpenModel.subtitle)
        detailItems.add("Nama Tujuan" to mOpenModel.title)

// Inflate and attach to layout
        detailItems.forEach { (label, value) ->
            val inflater = LayoutInflater.from(this)
            val itemView = inflater.inflate(R.layout.item_bill_detail, binding.llMoreContent, false)

            val tvNameContent = itemView.findViewById<TextView>(R.id.tv_name_content)
            val tvContent = itemView.findViewById<TextView>(R.id.tv_content)

            tvNameContent.text = label
            tvContent.text = value

            binding.llMoreContent.addView(itemView)
        }

        initViews()
    }


    private fun initViews() {
        val billingDetail = mOpenModel

        GeneralHelper.loadIconTransaction(
            this,
            billingDetail.iconPath,
            billingDetail.iconName.split("\\.".toRegex())
                .dropLastWhile { it.isEmpty() }.toTypedArray().get(0),
            binding.ivArea,
            GeneralHelper.getImageId(this, "bri")
        )

        GeneralHelper.loadIconTransaction(
            this,
            mAccountModel.imagePath,
            mAccountModel.imageName,
            binding.ivRekening,
            R.drawable.card_simpedes
        )

        binding.tvNameCust.text = billingDetail.title
        binding.tvNumberCust.text = String.format(
            GeneralHelper.getString(R.string.transaction_detail_content),
            billingDetail.subtitle,
            billingDetail.description
        )

        binding.tvNominal.text = mNominalString
        binding.tvAdminFee.text = mAdminString

        binding.bivSaveName.apply {
            setInputType(InputType.TYPE_CLASS_TEXT)
            setTextSize(SAVED_AS_TEXT_SIZE)
            addEndIcon(R.drawable.ic_clear_ns) {
                clearText()
            }
        }

        val nominal = mNominal.replace(".", "").toLongOrNull() ?: 0L
        val admin = mAdmin.replace(".", "").toLongOrNull() ?: 0L
        val total = nominal + admin

        val formattedTotal = "${Constant.CURRENCY}${GeneralHelper.formatNominal(total.toString())}"

        binding.btnSubmit.setAmountText(formattedTotal)
        binding.totalTagihan.text = formattedTotal

        binding.tvNumberAccount.text = if (mAccountModel.alias.equals("", ignoreCase = true)) mAccountModel.name else mAccountModel.alias
        binding.tvNominalAccount.text = mAccountModel.acoountString
    }

    override fun onGetDataConfirmation(generalConfirmationResponse: GeneralConfirmationResponse) {
        generalConfirmationResponse.let {
            val parameterKonfirmasiModel = ParameterKonfirmasiModel()
            presenter.getDataPayment(
                pin,
                it,
                isFromFastMenu,
                parameterKonfirmasiModel.isUsingC2
            )
        }
    }

    override fun onExceptionTrxExpired(desc: String) {
        val returnIntent = Intent()
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, desc)
        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    override fun onException(message: String) {
        GeneralHelperNewSkin.triggerVibration(this, Constant.VIBRATE_ERROR)
        if (GeneralHelperNewSkin.isContains(Constant.LIST_TYPE_GAGAL_GANGGUAN_SISTEM, message)) {
            GeneralHelperNewSkin.showErrorBottomDialog(this, message)
        } else if (message.contains(Constant.FP1561)) {
            OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
                fragmentManager = supportFragmentManager,
                imgPath = "",
                imgName = "ic_warning_illustration",
                titleTxt = GeneralHelper.getString(R.string.txt_limit_harian_title),
                subTitleTxt = GeneralHelper.getString(R.string.txt_limit_harian_desc),
                btnFirstFunction = {
                    // Dismiss
                },
                isClickableOutside = true,
                firstBtnTxt = GeneralHelper.getString(R.string.mengerti),
                showCloseButton = true
            )
        } else {
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
        }
    }

    override fun onExceptionLimitExceed(response: GeneralResponse?) {
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformationDismiss(
            fragmentManager = supportFragmentManager,
            imgPath = "",
            imgName = "ic_warning_illustration",
            titleTxt = GeneralHelper.getString(R.string.txt_limit_harian_title),
            subTitleTxt = GeneralHelper.getString(R.string.txt_limit_harian_desc),
            btnFirstFunction = {
                // Dismiss
            },
            isClickableOutside = true,
            firstBtnTxt = GeneralHelper.getString(R.string.mengerti),
            showCloseButton = true
        )
    }

    override fun onExceptionWH(onOnExceptionWH: onExceptionWH?) {
        // Do nothing
    }

    override fun onException01(message: String?) {
        GeneralHelperNewSkin.showGeneralErrorBottomDialog(this)
        isCompletedPin = false
    }

    override fun onSuccessGetPayment(receiptRevampResponse: ReceiptRevampResponse) {
        TransactionProcessActivity.launchIntent(this,
            isFromFastMenu, receiptRevampResponse, sumberDana = mAccountModel, receiptType = ReceiptType.EWALLET
        )
        setResult(Activity.RESULT_OK)
        finish()
    }

    override fun onSendPinComplete(pin: String) {
        isCompletedPin = true
        this.pin = pin ?: ""
        presenter.getDataConfirmation(
            mRefNum,
            mAccountModel.acoount,
            mNominal,
            binding.bivSaveName.getText(),
            mNote,
            isFromFastMenu
        )
    }

    override fun onLupaPin() {
        if (isFromFastMenu) LupaPinFastActivity.launchIntent(this)
        else LupaPinActivity.launchIntent(this)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            Constant.REQ_PAYMENT -> handlePaymentResult(resultCode, data)
            Constant.REQ_FORGET_PIN -> handleForgetPinResult(resultCode)
        }

    }

    private fun handlePaymentResult(resultCode: Int, data: Intent?) {
        setResult(resultCode, data)
        finish()
    }

    private fun handleForgetPinResult(resultCode: Int) {
        if (resultCode == Activity.RESULT_OK) {
            setResult(Activity.RESULT_OK)
            finish()
        }
    }

    fun mParameterModel(): ParameterModel {
        val parameterModel = ParameterModel()
        parameterModel.stringLabelTujuan = GeneralHelper.getString(R.string.nomor_tujuan)
        parameterModel.stringLabelNominal = GeneralHelper.getString(R.string.nominal)
        parameterModel.defaultIcon = R.drawable.pln_tagihan_listrik
        return parameterModel
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        showBackConfirmationBottomSheet()
    }

    private fun showBackConfirmationBottomSheet() {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            fragmentManager = supportFragmentManager,
            imgDrawable = R.drawable.ic_sad_illustration,
            imgName = "ic_sad_illustration",
            titleTxt = GeneralHelper.getString(R.string.dialog_exit_title_wv_paket),
            subTitleTxt = GeneralHelper.getString(R.string.txt_back_from_confirmation),
            btnFirstFunction = {
                // Close the dialog
            },
            btnSecondFunction = {
                navigateBackToInquiry()
            },
            isClickableOutside = true,
            withBgSecondBtn = false,
            firstBtnTxt = GeneralHelper.getString(R.string.txt_lanjutkan_transaksi),
            secondBtnTxt = GeneralHelper.getString(R.string.kembali),
            showCloseButton = true,
            showPill = true
        )
    }

    private fun navigateBackToInquiry() {
        val intent = Intent(this, InquiryDompetDigitalReskinActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        startActivity(intent)
        finish()
    }
}