package id.co.bri.brimo.ui.fragments.bukarekening

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.*
import id.co.bri.brimo.databinding.FragmentBottomReceiptBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.SizeHelper
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse

class BottomFragmentReceipt : BottomSheetDialogFragment() {

    private var _binding: FragmentBottomReceiptBinding? = null
    private val binding get() = _binding!!

    private var detailTransaksiRevampAdapter: DetailTransaksiRevampAdapter? = null
    private var sumberTransaksiRevAdapter: SumberTransaksiRevAdapter? = null
    private var totalTransaksiRevAdapter: TotalTransaksiRevAdapter? = null
    private var emas = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogReceipt)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentBottomReceiptBinding.inflate(inflater, container, false)
        val view = binding.root

        if (mtrxType == Constant.PAYMENT_SUBMIT_REGIST_DPLK) {
            binding.tvDetailTabungan.text = GeneralHelper.getString(R.string.txt_expand_title_open_dplk)
        }

        setupRecyclerViews()

        if (mPendingResponse.amountDataView == null) {
            binding.viewAmountDataView.visibility = View.GONE
        }

        if (mPendingResponse.totalDataView != null) {
            binding.rvTotal.setHasFixedSize(true)
            binding.rvTotal.layoutManager = LinearLayoutManager(
                context, RecyclerView.VERTICAL, false
            )
            totalTransaksiRevAdapter = TotalTransaksiRevAdapter(mPendingResponse.totalDataView, context)
            binding.rvTotal.adapter = totalTransaksiRevAdapter
        } else {
            binding.lyAbu.setBackgroundColor(GeneralHelper.getColor(R.color.whiteColor))
        }

        if (!emas) {
            SizeHelper.setMarginsView(context, binding.lyAbu, 0, 0, 0, 0)
            SizeHelper.setMarginsView(context, binding.rvTotal, 0, 0, 0, 0)
            binding.lyAbu.setBackgroundColor(GeneralHelper.getColor(R.color.whiteColor))
        }

        return view
    }

    private fun setupRecyclerViews() {
        binding.rvSumberDana.apply {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            detailTransaksiRevampAdapter = DetailTransaksiRevampAdapter(mPendingResponse.headerDataView, context)
            adapter = detailTransaksiRevampAdapter
        }

        binding.rvNomorRekening.apply {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            detailTransaksiRevampAdapter = DetailTransaksiRevampAdapter(mPendingResponse.transactionDataViewPending, context)
            adapter = detailTransaksiRevampAdapter
        }

        binding.rvTanggalTransaksi.apply {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            detailTransaksiRevampAdapter = DetailTransaksiRevampAdapter(mPendingResponse.referenceDataView, context)
            adapter = detailTransaksiRevampAdapter
        }

        binding.rvSetoranAwal.apply {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            sumberTransaksiRevAdapter = SumberTransaksiRevAdapter(mPendingResponse.amountDataView, context)
            adapter = sumberTransaksiRevAdapter
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        lateinit var mPendingResponse: ReceiptRevampResponse
        var mtrxType = ""

        @JvmStatic
        fun newInstance(pendingResponse: ReceiptRevampResponse, trxType: String = "") =
            BottomFragmentReceipt().apply {
                mPendingResponse = pendingResponse
                mtrxType = trxType
            }

        @JvmStatic
        fun newInstance(pendingResponse: ReceiptRevampResponse, isEmas: Boolean) =
            BottomFragmentReceipt().apply {
                emas = isEmas
                mPendingResponse = pendingResponse
            }
    }
}