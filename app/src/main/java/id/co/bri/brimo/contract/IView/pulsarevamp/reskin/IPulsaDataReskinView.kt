package id.co.bri.brimo.contract.IView.pulsarevamp.reskin

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.contract.IView.base.IBaseTransactionView
import id.co.bri.brimo.data.api.observer.ResExceptionErr
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import kotlinx.parcelize.Parcelize

interface IPulsaDataReskinView: IBaseTransactionView {
    fun onSuccess(res: PulsaDataResult)
    fun onExceptionReskin(exception: PulsaDataException)
}

sealed class PulsaDataException {
    data class KnownError(val code: String, val body: ResExceptionErr) : PulsaDataException()
    data class UnknownError(val code: String, val errorBody: Any?) : PulsaDataException()
}

enum class PulsaErrorCode(val code: String) {
    EXCEPTION_93("93"),
    EXCEPTION_12("12")
}

sealed class PulsaDataResult {
    data class Form(val data: FormPulsaDataResponse): PulsaDataResult()
    data class Receipt(val data: ReceiptRevampResponse): PulsaDataResult()
    data class Confirmation(val data: GeneralConfirmationResponse): PulsaDataResult()
    data class Favorite(val data: FavoriteType): PulsaDataResult()
    data class Other(val data: Any) : PulsaDataResult()
}

@Parcelize
data class AccountModelNs(
    @SerializedName("account") var account          : List<AccountModel>,
): Parcelable

@Parcelize
data class AccountListEntity (
    @SerializedName("account"            ) var account          : String? = null,
    @SerializedName("account_string"     ) var accountString    : String? = null,
    @SerializedName("name"               ) var name             : String? = null,
    @SerializedName("currency"           ) var currency         : String? = null,
    @SerializedName("card_number"        ) var cardNumber       : String? = null,
    @SerializedName("card_number_string" ) var cardNumberString : String? = null,
    @SerializedName("product_type"       ) var productType      : String? = null,
    @SerializedName("account_type"       ) var accountType      : String? = null,
    @SerializedName("sc_code"            ) var scCode           : String? = null,
    @SerializedName("default"            ) var default          : Int?    = null,
    @SerializedName("alias"              ) var alias            : String? = null,
    @SerializedName("minimum_balance"    ) var minimumBalance   : Int?    = null,
    @SerializedName("limit"              ) var limit            : Int?    = null,
    @SerializedName("limit_string"       ) var limitString      : String? = null,
    @SerializedName("image_name"         ) var imageName        : String? = null,
    @SerializedName("image_path"         ) var imagePath        : String? = null
): Parcelable