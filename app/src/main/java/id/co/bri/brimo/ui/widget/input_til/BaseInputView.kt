package id.co.bri.brimo.ui.widget.input_til

import android.app.Activity
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.text.Editable
import android.text.InputFilter
import android.text.SpannableString
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.core.view.marginBottom
import androidx.core.view.marginLeft
import androidx.core.view.marginRight
import androidx.core.view.marginTop
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ViewInputReskinBinding
import id.co.bri.brimo.domain.extension.margin
import id.co.bri.brimo.util.applyStateColor
import id.co.bri.brimo.util.attachNumpad
import id.co.bri.brimo.util.custom_numpad.CustomNumpadHelper
import id.co.bri.brimo.util.custom_numpad.NumpadType
import id.co.bri.brimo.util.extension.view.setMaxLength

class BaseInputView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    var endIconVisible: Boolean
        get() = binding.endIconContainer.visibility == View.VISIBLE
        set(value) {
            binding.endIconContainer.visibility = if (value && binding.endIconContainer.childCount > 0) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }

    private val binding: ViewInputReskinBinding =
        ViewInputReskinBinding.inflate(LayoutInflater.from(context), this)

    private val iconListeners = mutableMapOf<Int, () -> Unit>()

    var isError = false

    init {
        orientation = VERTICAL

        attrs?.let {
            val typedArray = context.obtainStyledAttributes(it, R.styleable.BaseInputView, 0, 0)
            val hint = typedArray.getString(R.styleable.BaseInputView_hintText)
            val prefix = typedArray.getString(R.styleable.BaseInputView_prefixText)
            val isExpanded = typedArray.getBoolean(R.styleable.BaseInputView_expanded, false)

            setHint(hint.orEmpty())
            setPrefixText(prefix.orEmpty())
            setExpandHint(isExpanded)

            typedArray.recycle()
        }
    }

    fun maxLength(max: Int) {
        binding.etInput.setMaxLength(max)
    }
    fun attachNumpad(
        activity: Activity,
        type: NumpadType,
        onPinComplete: (String) -> Unit,
        onAttached: ((CustomNumpadHelper) -> Unit)? = null,
        onFocusChanged: ((Boolean) -> Unit)? = null
    ) {
        binding.textInputLayout.attachNumpad(activity, type, onPinComplete, onAttached, onFocusChanged)
    }

    fun setExpandHint(isExpand: Boolean) {
        binding.textInputLayout.isExpandedHintEnabled = !isExpand
    }

    fun setPrefixText(prefix: String) {
        binding.textInputLayout.prefixText = prefix
    }

    fun setDistancePrefix(dp: Int) {
        (binding.textInputLayout.prefixTextView.layoutParams as? MarginLayoutParams)?.let {
            it.marginEnd = dp
            binding.textInputLayout.prefixTextView.layoutParams = it
        }
    }

    fun setHint(hint: CharSequence) {
        binding.textInputLayout.hint = hint
    }

    fun setTextColorHint(color: Int) {
        binding.textInputLayout.defaultHintTextColor = ContextCompat.getColorStateList(context, color)
    }

    fun setTextColorHint(colorStateList: ColorStateList) {
        binding.textInputLayout.defaultHintTextColor = colorStateList
    }

    fun setInputType(type: Int) {
        binding.etInput.inputType = type
    }

    fun setText(text: String) {
        binding.etInput.setText(text)
    }

    fun setError(text: String) {
        isError = true
        binding.tvError.visibility = VISIBLE
        binding.tvError.text = text
        binding.textInputLayout.setBackgroundResource(R.drawable.selector_input_field_error_ns)
        binding.textInputLayout.setBoxStrokeColorStateList(
            ContextCompat.getColorStateList(
                context,
                R.color.error80
            )!!
        )
    }

    fun clearError() {
        isError = false
        binding.tvError.visibility = GONE
        binding.textInputLayout.setBackgroundResource(R.drawable.selector_input_field_reskin)
        binding.textInputLayout.setBoxStrokeColorStateList(
            ContextCompat.getColorStateList(
                context,
                R.color.ns_greytheme
            )!!
        )
    }

    fun getText(): String {
        return binding.etInput.text?.toString().orEmpty()
    }

    fun clearText() {
        binding.etInput.text?.clear()
    }

    fun addTextChangedListener(watcher: TextWatcher) {
        binding.etInput.addTextChangedListener(watcher)
    }

    fun removeTextChangedListener(watcher: TextWatcher) {
        binding.etInput.removeTextChangedListener(watcher)
    }

    fun setInputTypefaceStyle(style: Int) {
        binding.etInput.setTypeface(binding.etInput.typeface, style)
    }

    fun setColorStyle(enabledColor: Int, disabledColor: Int) {
        binding.etInput.applyStateColor(
            enabledColor = enabledColor,
            disabledColor = disabledColor
        )
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        binding.etInput.isEnabled = enabled
        binding.textInputLayout.isEnabled = enabled
    }

    override fun setFocusable(focusable: Boolean) {
        super.setFocusable(focusable)
        binding.etInput.isFocusable = focusable
        binding.textInputLayout.isFocusable = focusable
    }

    override fun setClickable(clickable: Boolean) {
        super.setClickable(clickable)
        binding.etInput.isClickable = clickable
        binding.textInputLayout.isClickable = clickable
    }

    fun setOnInputFocusChangeListener(listener: View.OnFocusChangeListener) {
        binding.etInput.onFocusChangeListener = listener
    }

    fun addEndIcon(
        @DrawableRes iconRes: Int,
        sizeDp: Int = 30,
        paddingDp: Int = 4,
        marginDp: Int = 10,
        onClick: OnClickListener,
    ) {
        val iconView = ImageView(context).apply {
            setImageResource(iconRes)
            setPadding(dpToPx(paddingDp), dpToPx(paddingDp), dpToPx(paddingDp), dpToPx(paddingDp))
            layoutParams = LayoutParams(dpToPx(sizeDp), dpToPx(sizeDp)).apply {
                setMargins(0, 0, dpToPx(marginDp), 0)
            }
            setOnClickListener(onClick)
            adjustViewBounds = true
            scaleType = ImageView.ScaleType.FIT_CENTER
        }

        val id = View.generateViewId()
        iconView.id = id
        iconListeners[id] = {
            onClick.onClick(iconView)
        }
        binding.endIconContainer.addView(iconView)

        adjustEditTextPadding()
    }


    fun removeAllEndIcons() {
        binding.endIconContainer.removeAllViews()
        iconListeners.clear()
        adjustEditTextPadding()
    }

    private fun adjustEditTextPadding() {
        binding.endIconContainer.post {
            val iconWidth = binding.endIconContainer.width
            binding.etInput.setPaddingRelative(
                binding.etInput.paddingStart,
                binding.etInput.paddingTop,
                iconWidth,
                binding.etInput.paddingBottom
            )
        }
    }

    fun setStartIconWithUrl(url: String) {
        binding.textInputLayout.apply {
            if(url.isEmpty()) {
                startIconDrawable = null
                setStartIconTintList(null)
            } else {
                Glide.with(this)
                    .asBitmap()
                    .load(url)
                    .circleCrop()
                    .into(object : CustomTarget<Bitmap>() {
                        override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                            val iconView = LinearLayout(context).apply {
                                orientation = LinearLayout.HORIZONTAL

                                val imageView = ImageView(context).apply {
                                    layoutParams = LinearLayout.LayoutParams(dpToPx(40), dpToPx(40))
                                    setImageBitmap(resource)
                                }

                                addView(imageView)
                            }

                            val drawable = convertViewToDrawable(iconView)
                            startIconDrawable = drawable
                            setStartIconTintList(null)
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {}
                    })
            }
        }
    }

    fun setFilters(filters: Array<InputFilter>) {
        binding.etInput.filters = filters
    }

    fun isHintExpanded(): Boolean {
        return binding.textInputLayout.isExpandedHintEnabled
    }

    fun setSelection(index: Int) {
        binding.etInput.setSelection(index)
    }

    fun setTextSize(textSizeSp: Float) {
        binding.etInput.textSize = textSizeSp
    }

    fun convertViewToDrawable(view: View): Drawable {
        view.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        view.layout(0, 0, view.measuredWidth, view.measuredHeight)
        val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        view.draw(canvas)
        return BitmapDrawable(resources, bitmap)
    }

    private fun dpToPx(dp: Int): Int {
        return (dp * resources.displayMetrics.density).toInt()
    }
}