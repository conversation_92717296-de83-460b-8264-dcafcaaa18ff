package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputType;
import android.view.View;

import androidx.annotation.Nullable;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.IFormEditSavedPresenter;
import id.co.bri.brimo.contract.IView.IFormEditSavedView;
import id.co.bri.brimo.databinding.ActivityEditTersimpanReskinBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity;

public class FormEditSavedReskinActivity extends NewSkinBaseActivity implements IFormEditSavedView {

    private ActivityEditTersimpanReskinBinding binding;

    @Inject
    IFormEditSavedPresenter<IFormEditSavedView> editSavedPresenter;


    static SavedResponse lastSavedResponse;
    static int mType;
    static int mPosition;
    static int mDefaultIcon;
    static String mUrlUpdate;

    private String newName;
    private String[] str1;
    private String savedId = "";
    private String paymentType = "";
    private String initial;
    static String lblService = "";

    static String lblTujuanHint = "";


    public static void launchIntent(Activity caller, SavedResponse savedResponse, int position, int defaultIcon, String urlUpdate, String labelServis, String labelTujuanHint) {
        Intent intent = new Intent(caller, FormEditSavedReskinActivity.class);
        lastSavedResponse = savedResponse;
        mPosition = position;
        mDefaultIcon = defaultIcon;
        lblService = labelServis;
        lblTujuanHint = labelTujuanHint;
        mUrlUpdate = urlUpdate;

        caller.startActivityForResult(intent, Constant.REQ_EDIT_SAVED);
    }


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityEditTersimpanReskinBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupView(lastSavedResponse);

        GeneralHelperNewSkin.setToolbar(this, binding.toolbar.toolbar, getString(R.string.ubah_nama));

        injectDependency();

    }

    @Override
    protected void onResume() {
        super.onResume();
        if (editSavedPresenter != null) {
            editSavedPresenter.setView(this);
            editSavedPresenter.start();
        }
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
    }

    /**
     * Helper method to detect if this is a digital wallet transaction
     * Digital wallet transactions have wallet names in subtitle and phone numbers in description
     */
    private boolean isDigitalWalletTransaction(String subtitle, String description) {
        if (subtitle == null || description == null) return false;

        // Check if description looks like a phone number (starts with 0 and contains only digits)
        String cleanDescription = description.trim();
        if (cleanDescription.startsWith("0") && cleanDescription.matches("^0\\d{8,12}$")) {
            return true;
        }

        // Check if description starts with +62 (Indonesian country code)
        if (cleanDescription.startsWith("+62") && cleanDescription.matches("^\\+62\\d{8,12}$")) {
            return true;
        }

        return false;
    }

    void setupView(SavedResponse response) {

        newName = response.getTitle();
        binding.etSavedName.setText(response.getTitle());
        binding.etSavedName.addTextChangedListener(activityTextListener);
        binding.etSavedName.setInputType(InputType.TYPE_CLASS_TEXT);

        // Handle different data formats based on transaction type
        String subtitle = response.getSubtitle();
        String description = response.getDescription();

        // Check if this is a digital wallet transaction (phone number format)
        if (isDigitalWalletTransaction(subtitle, description)) {
            // For digital wallet: subtitle = wallet name, description = phone number
            binding.etWilayah.setText(subtitle != null ? subtitle.trim() : ""); // Wallet name

            // Format phone number: change leading 0 to "+62 "
            String phoneNumber = description != null ? description.trim() : "";
            if (phoneNumber.startsWith("0")) {
                phoneNumber = "+62 " + phoneNumber.substring(1);
            }
            binding.lblTujuan.setText(phoneNumber); // Formatted phone number

            binding.lblTujuan.setHint(lblTujuanHint);
            binding.regionTextview.setText(lblService);
        } else if (subtitle != null && subtitle.contains("-")) {
            // For PDAM: subtitle = "Region - Customer Number"
            String[] subtitleParts = subtitle.split("-");
            if (subtitleParts.length >= 2) {
                binding.etWilayah.setText(subtitleParts[0].trim()); // Region
                binding.lblTujuan.setText(subtitleParts[1].trim()); // Customer Number
            } else {
                binding.etWilayah.setText(subtitle.trim());
                binding.lblTujuan.setText("");
            }
        } else {
            // Fallback for other formats
            binding.etWilayah.setText(subtitle != null ? subtitle.trim() : "");
            binding.lblTujuan.setText(description != null ? description.trim() : "");
        }

        binding.lblTujuan.setInputTypefaceStyle(android.graphics.Typeface.BOLD);
        binding.tvInisialOpen.setText(GeneralHelper.formatInitialName(response.getTitle()));

        //Set Image Circle
        if (lastSavedResponse.getListType().equals("image")) {
            if (lastSavedResponse.getIconName() == null) {
                binding.llLogoOpen.setVisibility(View.GONE);
                binding.rlInisialOpen.setVisibility(View.VISIBLE);
            } else {
                binding.llLogoOpen.setVisibility(View.VISIBLE);
                binding.rlInisialOpen.setVisibility(View.GONE);
            }
        } else {
            binding.llLogoOpen.setVisibility(View.GONE);
            binding.rlInisialOpen.setVisibility(View.VISIBLE);

        }

        //load icon transaction
        GeneralHelper.loadIconTransaction(
                this,
                lastSavedResponse.getIconPath(),
                lastSavedResponse.getIconName(),
                binding.ivIconOpen,
                mDefaultIcon);

        // Disable input fields for etWilayah and lblTujuan
        binding.etWallet.setEnabled(false);
        binding.etWilayah.setEnabled(false);
        binding.etWilayah.setFocusable(false);
        binding.etWilayah.setClickable(false);
        binding.lblTujuan.setHintTextColor(R.color.text_disabled_default_ns);

        binding.lblTujuan.setEnabled(false);
        binding.lblTujuan.setFocusable(false);
        binding.lblTujuan.setClickable(false);

        binding.btnSubmit.setOnClickListener(view -> onSubmit());
        if (binding.etSavedName.getText().equals(lastSavedResponse.getTitle())) {
            binding.btnSubmit.setEnabled(false);
        }

    }

    @Override
    protected void afterText(Editable editable) {
        if (binding.etSavedName.getText().equals(lastSavedResponse.getTitle())) {
            binding.btnSubmit.setEnabled(false);
        } else binding.btnSubmit.setEnabled(!binding.etSavedName.getText().isEmpty());
    }

    void onSubmit() {

        if (!binding.etSavedName.getText().isEmpty()) {
            newName = binding.etSavedName.getText();

            str1 = lastSavedResponse.getValue().split("\\|");

            if (str1.length > 1) {
                savedId = str1[0];
                paymentType = str1[1];
            } else if (str1.length > 0) {
                savedId = str1[0];
                paymentType = ""; // Default empty value if paymentType is not available
            }

            editSavedPresenter.setUpdateTitle(mUrlUpdate, mPosition, newName, savedId, paymentType);

        } else {
            showSnackbarErrorMessage(GeneralHelper.getString(R.string.txt_name_cant_be_empty), BaseActivity.ALERT_ERROR, this, false);
        }

    }


    @Override
    public void onSuccessGetUpdateTitle(int item, String newName) {

        Intent returnIntent = new Intent();
        returnIntent.putExtra(Constant.TAG_TITLE, newName);
        returnIntent.putExtra(Constant.TAG_POSITION, mPosition);

        this.setResult(RESULT_OK, returnIntent);
        this.finish();


    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}