package id.co.bri.brimo.nfcpayment

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.nfc.NfcAdapter
import android.nfc.NfcManager
import android.nfc.cardemulation.CardEmulation
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AppCompatActivity
import java.util.Timer
import java.util.TimerTask

class NFCPayment(
    context: Context
) {

    private var nfcAdapter: NfcAdapter? = null
    private var cardEmulation: CardEmulation? = null
    private var timer: Timer? = null
    private var isActivityActive: Boolean = false

    companion object {

        private const val TICKER_TIME = 5000L
        private const val PKG_SERVICE = "id.co.bri.brimo"
        private const val CLS_SERVICE = "id.co.bri.brimo.nfcpayment.NFCPaymentService"

        private var instance: NFCPayment? = null

        @JvmStatic
        fun getInstance(context: Context): NFCPayment {
            if (instance == null) {
                synchronized(NFCPayment::class.java){
                    if (instance == null) {
                        instance = NFCPayment(context.applicationContext)
                    }
                }
            }
            return instance ?: NFCPayment(context.applicationContext)
        }
    }

    init {
        val nfcManager = context.getSystemService(Context.NFC_SERVICE) as NfcManager
        nfcAdapter = nfcManager.defaultAdapter
        if (nfcAdapter == null) {
            nfcAdapter = NfcAdapter.getDefaultAdapter(context)
        }
        nfcAdapter?.let {
            cardEmulation = CardEmulation.getInstance(nfcAdapter)
        }
    }

    /**
     * check whether the device has nfc feature
     * @return Boolean
     */
    fun isNfcAvailable() : Boolean {
        return nfcAdapter != null
    }

    /**
     * check whether the device has nfc feature && is enabled
     * @return Boolean
     */
    fun isNfcEnabled(): Boolean {
        return nfcAdapter != null && nfcAdapter?.isEnabled == true
    }

    fun disableReaderMode(activity: AppCompatActivity){
        nfcAdapter?.disableReaderMode(activity)
    }

    fun disableReaderMode(activity: ComponentActivity){
        nfcAdapter?.disableReaderMode(activity)
    }

    /**
     * running ticker for each ticker timer to check nfc payment status
     */
    fun checkStatusTicker(
        onTick: () -> Unit,
    ) {
        timer = Timer()
        timer?.scheduleAtFixedRate(object : TimerTask(){
            override fun run() {
                onTick.invoke()
            }
        }, 0L, TICKER_TIME)
    }

    /**
     * stop ticker if payment is success or failed
     */
    fun stopCheckStatusTicker() {
        timer?.cancel()
    }

    fun getCardEmulation(): CardEmulation? = cardEmulation

    fun isDefaultService(): Boolean {
        return cardEmulation?.isDefaultServiceForCategory(
            ComponentName(PKG_SERVICE, CLS_SERVICE),
            CardEmulation.CATEGORY_PAYMENT
        ) == true
    }

    fun changeDefaultPaymentService(context: Context) {
        val intent = Intent(CardEmulation.ACTION_CHANGE_DEFAULT)
        intent.putExtra(
            CardEmulation.EXTRA_SERVICE_COMPONENT,
            ComponentName(PKG_SERVICE, CLS_SERVICE),
        )
        intent.putExtra(CardEmulation.EXTRA_CATEGORY, CardEmulation.CATEGORY_PAYMENT)
        context.startActivity(intent)
    }

    fun setActivityStatus(isActive: Boolean) {
        isActivityActive = isActive
    }

    fun isActivityActivated(): Boolean = isActivityActive

}