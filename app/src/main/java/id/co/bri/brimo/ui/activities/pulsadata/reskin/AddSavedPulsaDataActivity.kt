package id.co.bri.brimo.ui.activities.pulsadata.reskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.core.graphics.toColorInt
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataException
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataResult
import id.co.bri.brimo.databinding.ActivityAddSavedPulsadataNsBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.listrikrevamp.reskin.FavoriteEvent
import id.co.bri.brimo.util.RxBus
import javax.inject.Inject

class AddSavedPulsaDataActivity: NewSkinBaseActivity(), IPulsaDataReskinView {
    private var _binding: ActivityAddSavedPulsadataNsBinding? = null
    protected val binding get() = _binding!!

    @Inject
    lateinit var presenter: IPulsaDataReskinPresenter<IPulsaDataReskinView>

    var activityTextListener: TextWatcher = object : TextWatcher {
        override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            beforeText()
        }

        override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            changeText(charSequence, i, i1, i2)
        }

        override fun afterTextChanged(editable: Editable) {
            afterText(editable)

            binding.etSavedName.removeAllEndIcons()
            if (editable.toString().isNotEmpty()) {
                binding.etSavedName.addEndIcon(R.drawable.ic_clear_ns, sizeDp = 24, marginDp = 5) {
                    binding.etSavedName.clearText()
                }
            }
        }
    }

    companion object {
        const val TAG = "AddSavedListrikActivity"

        private var bindInquiry: ReqInquiry?= null

        @JvmStatic
        fun launchIntent(caller: Activity, data: ReqInquiry, fromFastMenu: Boolean) {
            isFromFastMenu = fromFastMenu
            bindInquiry = data
            caller.apply {
                startActivityForResult(
                    Intent(
                        this,
                        AddSavedPulsaDataActivity::class.java
                    ), Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityAddSavedPulsadataNsBinding.inflate(layoutInflater)

        setContentView(binding.root)
        onBindIntentData()

        injectDependency()

        onBindView()
    }

    private fun onBindView() {
        binding.bslContent.setTextToolbar(this@AddSavedPulsaDataActivity, "Tambah Daftar Favorit")

        bindInquiry?.let {
            binding.bivNoPelanggan.apply {
                setColorStyle("#181C21".toColorInt(), "#181C21".toColorInt())
                setEnabled(false)
                setText(it.phone)
                setExpandHint(true)
                setDistancePrefix(resources.getDimensionPixelSize(R.dimen.size_8dp))
                setStartIconWithUrl(it.identityPhone.iconPath)
            }

            binding.etSavedName.apply {
                setText(it.name)
                addTextChangedListener(activityTextListener)
                addEndIcon(
                    R.drawable.ic_clear_ns,
                    sizeDp = 24,
                ) {
                    clearText()
                }
            }

            binding.btnSubmit.setOnClickListener { view ->
                showProgress()
                presenter.executeRequest(
                    url = GeneralHelper.getString(R.string.url_update_pulsa),
                    requestParam = SavedListNs(
                        saveAs = binding.etSavedName.getText(),
                        savedId = it.savedId.toInt()
                    ),
                    responseType = RestResponse::class.java,
                    isSaved = FavoriteType.editFavorite
                )
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@AddSavedPulsaDataActivity
            start()
        }
    }

    private fun onBindIntentData() {
    }

    override fun changeText(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
        super.changeText(charSequence, i, i1, i2)

        val input = charSequence.toString()

        binding.etSavedName.removeAllEndIcons()

        bindInquiry?.let {
            binding.btnSubmit.isEnabled = input.isNotEmpty() && input!=it.name
        }

        if (input.isNotEmpty()) {
            binding.etSavedName.addEndIcon(
                R.drawable.ic_clear_ns,
                sizeDp = 24,
            ) {
                binding.etSavedName.clearText()
            }
        }
    }

    override fun onSuccess(res: PulsaDataResult) {
        when (res) {
            is PulsaDataResult.Favorite -> {
                hideProgress()
                RxBus.send(FavoriteEvent(isEdit = true))
                finish()
            }
            else -> Unit
        }
    }

    override fun onExceptionReskin(exception: PulsaDataException) {
        showSnackbar("Terjadi gangguan sistem. Silakan coba lagi, ya.", ALERT_ERROR)
    }

    override fun onSuccessAccountList(
        accountList: MutableList<AccountModel>,
        mainAccount: AccountModel
    ) {
    }
}