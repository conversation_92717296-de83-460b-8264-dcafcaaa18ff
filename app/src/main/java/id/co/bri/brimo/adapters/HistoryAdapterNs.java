package id.co.bri.brimo.adapters;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.SystemClock;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Filter;
import android.widget.Filterable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ItemHistoryDompetDigitalNsBinding;
import id.co.bri.brimo.databinding.ItemSavedBinding;
import id.co.bri.brimo.databinding.ItemSavedNsBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.HistoryResponse;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.models.apimodel.response.SavedResponse;
import io.rmiri.skeleton.master.AdapterSkeleton;

public class HistoryAdapterNs extends AdapterSkeleton<HistoryResponse, HistoryAdapterNs.MyViewHolder> implements Filterable {

    private List<HistoryResponse> historyResponses, dataFiltered;
    private Context mContext;
    private int defaultIconResource;
    ClickItem clickItem;
    private int lastPosition = -1;
    private long mLastClickTime = 0;

    private long animationOffset = 100;
    private boolean mIsFromFastMenu = false;

    private String type = "";

    private String searchQuery = "";

    public void setSearchQuery(String searchQuery) {
        this.searchQuery = searchQuery;
    }

    @Override
    public Filter getFilter() {
        return new Filter() {
            @Override
            protected FilterResults performFiltering(CharSequence charSequence) {
                if (charSequence.toString().isEmpty())
                    dataFiltered = historyResponses;
                else {
                    ArrayList<HistoryResponse> daftarSimpans = new ArrayList<>();
                    for (HistoryResponse d : historyResponses) {
                        if (d.getTitle().toLowerCase().contains(charSequence.toString().toLowerCase()))
                            daftarSimpans.add(d);
                    }
                    dataFiltered = daftarSimpans;
                }
                FilterResults filterResults = new FilterResults();
                filterResults.values = dataFiltered;
                return filterResults;
            }

            @Override
            protected void publishResults(CharSequence charSequence, FilterResults filterResults) {
                dataFiltered = (List<HistoryResponse>) filterResults.values;
                notifyDataSetChanged();
            }
        };
    }

    public interface ClickItem {
        void onClickHistoryItem(HistoryResponse historyResponse);
    }

    public HistoryAdapterNs(Context context, List<HistoryResponse> historyResponses, ClickItem clickItem, int defaultIconResource) {
        this.mContext = context;
        this.historyResponses = historyResponses;
        this.dataFiltered = historyResponses;
        this.clickItem = clickItem;
        this.defaultIconResource = defaultIconResource;
    }

    public HistoryAdapterNs(Context context, List<HistoryResponse> historyResponses, ClickItem clickItem, int defaultIconResource, boolean isFromFastMenu) {
        this(context, historyResponses, clickItem, defaultIconResource);
        this.mIsFromFastMenu = isFromFastMenu;
    }

    public HistoryAdapterNs(Context context, List<HistoryResponse> historyResponses, ClickItem clickItem, int defaultIconResource, boolean isFromFastMenu, String type) {
        this(context, historyResponses, clickItem, defaultIconResource);
        this.type = type;
        this.mIsFromFastMenu = isFromFastMenu;
    }

    public void setHistoryResponses(List<HistoryResponse> historyResponses) {
        this.historyResponses = historyResponses;
    }

    /**
     * Here is the key method to apply the animation
     */
    private void setAnimation(View viewToAnimate, int position) {
        // If the bound view wasn't previously displayed on screen, it's animated
        if (position > lastPosition) {
            Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.scale_fade_in);
            animation.setStartOffset((long) lastPosition * animationOffset);
            viewToAnimate.startAnimation(animation);
            lastPosition = position;
        }
    }


    @NonNull
    @Override
    public HistoryAdapterNs.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MyViewHolder(ItemHistoryDompetDigitalNsBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull HistoryAdapterNs.MyViewHolder holder, @SuppressLint("RecyclerView") int position) {
        HistoryResponse historyResponse = dataFiltered.get(position);
        String currency = "";

        if(type.equals("listrik")) {
            if(historyResponse.getValue().split("\\|").length>2) {
                currency = historyResponse.getValue().split("\\|")[2];
            }
        } else if(type.equals("pulsa")) {
            currency = historyResponse.getValue().split("\\|")[1];
        }

        String title = historyResponse.getTitle();
        boolean loadImage = false;

        if (historyResponse.getListType().equals(Constant.IMAGE_LIST_TYPE)) {
            holder.binding.llLogoSaved.setVisibility(View.VISIBLE);
            holder.binding.rlInisialSaved.setVisibility(View.GONE);
            //load icon transaction
            loadImage = GeneralHelper.loadIconTransactionSaved(
                    mContext,
                    historyResponse.getIconPath(),
                    historyResponse.getIconName(),
                    holder.binding.ivIconSaved,
                    new RequestListener() {
                        @Override
                        public boolean onLoadFailed(@Nullable GlideException e, Object model, Target target, boolean isFirstResource) {
                            loadInitialName(holder, title);
                            return false;
                        }

                        @Override
                        public boolean onResourceReady(Object resource, Object model, Target target, DataSource dataSource, boolean isFirstResource) {
                            return false;
                        }


                    });
        } else {
            loadInitialName(holder, title);
        }

        //gagal load image
        if (!loadImage)
            loadInitialName(holder, title);

        if (historyResponse.getName() == null || historyResponse.getName().isEmpty()){
            holder.binding.tvName.setVisibility(View.GONE);
        }else{
            holder.binding.tvName.setVisibility(View.VISIBLE);
        }
        holder.binding.tvName.setText(historyResponse.getName());
        holder.binding.tvTitle.setText(highlightSearchText(title, searchQuery));
        holder.binding.tvNomor.setText(historyResponse.getSubtitle());

        holder.binding.llSaved.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                    return;
                }
                mLastClickTime = SystemClock.elapsedRealtime();
                clickItem.onClickHistoryItem(historyResponse);
            }
        });

        holder.binding.tvInisialSaved.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                    return;
                }
                mLastClickTime = SystemClock.elapsedRealtime();
                clickItem.onClickHistoryItem(historyResponse);
            }
        });

        holder.binding.ivIconSaved.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                    return;
                }
                mLastClickTime = SystemClock.elapsedRealtime();
                clickItem.onClickHistoryItem(historyResponse);
            }
        });

        if(!currency.isEmpty()) {
            holder.binding.tvNominalTrx.setText(GeneralHelper.formatNominalIDR(
                    "Rp",
                    currency
            ));
        }

        //myViewHolder.imgFav.setOnClickListener(v->clickItem.onClickNonFavoritItem(historyResponse,position));
        // call Animation function
        setAnimation(holder.itemView, position);
    }

    private CharSequence highlightSearchText(String fullText, String searchText) {
        if (searchText == null || searchText.isEmpty() || fullText == null) {
            return fullText;
        }

        String lowerCaseFullText = fullText.toLowerCase();
        String lowerCaseSearchText = searchText.toLowerCase();
        int startPos = lowerCaseFullText.indexOf(lowerCaseSearchText);

        if (startPos != -1) {
            int endPos = startPos + searchText.length();
            Spannable spannable = new SpannableString(fullText);
            int highlightColor = ContextCompat.getColor(mContext, R.color.primary_ns_main);
            ForegroundColorSpan highlightSpan = new ForegroundColorSpan(highlightColor);
            spannable.setSpan(highlightSpan, startPos, endPos, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            return spannable;
        } else {
            return fullText;
        }
    }

    private void loadInitialName(HistoryAdapterNs.MyViewHolder myViewHolder, String title) {
        myViewHolder.binding.llLogoSaved.setVisibility(View.GONE);
        myViewHolder.binding.rlInisialSaved.setVisibility(View.VISIBLE);
        myViewHolder.binding.tvInisialSaved.setText(GeneralHelper.formatInitialName(title));
    }


    public static class MyViewHolder extends RecyclerView.ViewHolder {
        ItemHistoryDompetDigitalNsBinding binding;

        public MyViewHolder(@NonNull ItemHistoryDompetDigitalNsBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    @Override
    public int getItemCount() {
        return (dataFiltered != null) ? dataFiltered.size(): 0;
    }
}