package id.co.bri.brimo.payment.core.design.component

import android.content.Context
import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImagePainter
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.core.design.theme.Color_7B90A6

@Composable
fun ImageAsync(
    context: Context,
    url: String,
    initial: String,
    size: Int,
    color: Color = Color.White
) {
    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(context)
            .data(url)
            .bitmapConfig(Bitmap.Config.ARGB_8888)
            .placeholder(R.drawable.thumbnail)
            .size(size)
            .build()
    )

    when (painter.state) {
        is AsyncImagePainter.State.Success -> {
            Image(
                painter = painter,
                contentDescription = null,
                modifier = Modifier
                    .size(size.dp)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop
            )
        }

        else -> {
            val initial = (initial.firstOrNull() ?: "").toString().uppercase()

            Box(
                modifier = Modifier
                    .background(color, CircleShape)
                    .size(size.dp), contentAlignment = Alignment.Center
            ) {
                Text(
                    text = initial,
                    color = Color_7B90A6,
                    fontWeight = FontWeight.SemiBold,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}
