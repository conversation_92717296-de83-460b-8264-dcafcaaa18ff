package id.co.bri.brimo.ui.activities.bukarekeningreskin

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.Selection
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.bukarekeningnewskin.RekomendasiNewSkinAdapter
import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IInquiryProductTabunganRevPresenter
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IInquiryProductTabunganRevView
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.ActivityInquiryProductTabunganNewSkinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.ParameterPilihKantorModel
import id.co.bri.brimo.models.apimodel.request.BranchRequest
import id.co.bri.brimo.models.apimodel.request.bukarekening.KonfirmasiTabunganRequest
import id.co.bri.brimo.models.apimodel.response.BranchResponse
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.bukarekening.JenisTabunganResponse
import id.co.bri.brimo.models.apimodel.response.ListKantorResponse.Office
import id.co.bri.brimo.models.apimodel.response.bukarekening.InquiryOpenAccResponse
import id.co.bri.brimo.models.apimodel.response.bukarekening.RecomInitialDepositItem
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.bukarekening.PilihanKantorGeneralActivity
import id.co.bri.brimo.ui.fragments.FragmentDialogNoImageRevamp
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentNewSkin
import id.co.bri.brimo.ui.fragments.bukarekening.BottomSheetPilihKantorFragment
import java.util.function.Consumer
import javax.inject.Inject

open class InquiryProductTabunganNewSkinActivity : NewSkinBaseActivity(), IInquiryProductTabunganRevView,
        View.OnClickListener, SumberDanaFragmentNewSkin.SelectSumberDanaNewSkinInterface,
    RekomendasiNewSkinAdapter.OnItemClickListener, FragmentDialogNoImageRevamp.DialogDefaultListener {
    lateinit var mListAccountModel: List<AccountModel>
    private var mListFailed: List<Int> = mutableListOf()
    private lateinit var adapterRekomendasi : RekomendasiNewSkinAdapter
    var errorMessage: String? = null
    var fragmentDialogNoImageRevamp: FragmentDialogNoImageRevamp? = null

    lateinit var model: AccountModel
    lateinit var office: Office

    var saldoNominal = 0.0

    var brImoPrefRepository = BRImoPrefRepository(this)

    var counter = 0
    var saldo = 0.0
    var saldoToKonfirimasi = 0.0
    var msaldoString: String? = null
    var akunDefault: String? = null
    private var saldoHold: Boolean = false


    var urlLokasiSendiri: String? = null
    var urlLokasiPencarian: String? = null
    var urlInquiry: String? = null
    var officeName : String? = null
    var officeCode : String? = null
    var isChecked = false

    private var permissionLocation = android.Manifest.permission.ACCESS_FINE_LOCATION
    private lateinit var binding: ActivityInquiryProductTabunganNewSkinBinding

    private val TAG = "InquiryProductTabunganA"

    @Inject
    lateinit var presenter1: IInquiryProductTabunganRevPresenter<IInquiryProductTabunganRevView>

    companion object{

        var product: JenisTabunganResponse.Product? = null

        var inquiry : InquiryOpenAccResponse? = null

        var mUrlKonfirmasi : String = ""
        var mUrlPayment : String = ""

        fun launchIntent(
                caller: Activity, productList: JenisTabunganResponse.Product,
                inquiryList : InquiryOpenAccResponse, urlKonfirmasi : String, urlPayment : String
        ) {
            val intent = Intent(caller, InquiryProductTabunganNewSkinActivity::class.java)
            product = productList
            inquiry = inquiryList
            mUrlKonfirmasi = urlKonfirmasi
            mUrlPayment = urlPayment
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)

        }

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInquiryProductTabunganNewSkinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()

        binding.btnRekeningBaru.setOnClickListener(this)
        binding.ivScroll.setOnClickListener(this)
        binding.ivClose.setOnClickListener(this)
        binding.ivScrollAccount.setOnClickListener(this)
        binding.llPilihSumberDana.setOnClickListener(this)
        binding.tvKcpTerdaftar.setOnClickListener(this)


        when (product?.type) {
            Constant.OPEN_ACCOUNT_GENERAL -> setUrlGeneral()
            Constant.OPEN_ACCOUNT_S3f -> setUrlGeneral()

            else -> {}
        }
//
        binding.etSetoranAwal.setOnFocusChangeListener { view, b ->
            if (b){
                binding.ivClose.visibility = View.GONE
            }else{
                binding.ivClose.visibility = View.VISIBLE
            }
        }

        binding.llBack.setOnClickListener {
            onBackPressed()
        }

    }

    private fun setUrlGeneral(){
        urlLokasiSendiri = GeneralHelper.getString(R.string.url_s3f_lokasi_sendiri)
        urlLokasiPencarian = GeneralHelper.getString(R.string.url_s3f_lokasi_pencarian)
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter1.view = this
        presenter1.start()
        presenter1.setUrlKonfirmasi(mUrlKonfirmasi)
        presenter1.setUrlPilihKode(GeneralHelper.getString(R.string.url_pilih_branch_by_kode))
    }

    fun validasiButton(isOn: Boolean) {
        if (isOn) {
            binding.btnRekeningBaru.isEnabled = true
        } else {
            binding.btnRekeningBaru.isEnabled = false
        }
    }

    override fun setDefaultSaldo(saldoDouble: Double, saldoString: String?, account: String?,saldoHoldPref : Boolean) {
        msaldoString = saldoString
        saldo = saldoDouble
        akunDefault = account
        saldoHold = saldoHoldPref

        setupAccount(saldoDouble)
        setupView()
    }

    override fun onSuccessBranch(branchResponse: BranchResponse) {
        binding.tvKcpTerdaftar.text =branchResponse.branchDetails!!.branchDescription
        officeName = branchResponse.branchDetails.branchDescription
        officeCode = branchResponse.branchDetails.branch
        office = Office(
                "",
                officeName,
                "",
                officeCode,
                "",
                "",
        )
    }

    fun setupAccount(saldoDefault : Double) {
        //List Account
        if (inquiry!!.accountList.size > 0) {
            mListAccountModel = inquiry!!.accountList
        }

        //get account default
        for (accountModel in mListAccountModel) {
            if (accountModel.isDefault == 1) {
//                isChecked = true
                model = accountModel
                break
            } else {
//                isChecked = true
                model = mListAccountModel[0]
            }
        }

        loadImage(model)

        //jika get minimum tidak null
        saldo = if (model.minimumBalance != null) {
            saldoDefault - model.minimumBalance
        } else {
            saldoDefault
        }

        if (isFromFastMenu) {
            binding.tvSaldoRek.visibility = View.GONE
        } else {
            binding.tvSaldoRek.visibility = View.VISIBLE
            if (model.acoount != null) {
                saldoToKonfirimasi = saldoDefault
                if (model.acoount == akunDefault && msaldoString != "-")
                    binding.tvSaldoRek.text =
                            GeneralHelper.formatNominalIDR(model.currency, msaldoString)
                else binding.tvSaldoRek.text = "-"
            }
        }

        if (model.acoountString != null) {
            binding.tvNoRek.text = model.acoountString
        } else {
            binding.tvNoRek.text = "-"
        }
    }

    private fun loadImage(model1 : AccountModel) {
        binding.apply {
            if (model1.imagePath != null) {
                if (!model1.imagePath.equals("", ignoreCase = true)) {
                    GeneralHelper.loadImageUrl(
                        this@InquiryProductTabunganNewSkinActivity,
                        model1.imagePath,
                        ivIconRek,
                        R.drawable.bri,
                        0
                    )
                } else {
                    ivIconRek.setImageResource(R.drawable.bri)
                }
            } else {
                ivIconRek.setImageResource(R.drawable.bri)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun setupView(){
        binding.apply {
            var productName = ""
            when (inquiry?.minimumInitialDeposit?.minimumFloat?.toInt()) {
                50000 -> {
                    if (product?.productType.equals("S3")) {
                        productName = "Simpedes Bisa"
                    } else {
                        productName = "Simpedes"
                    }
                }
                250000 -> {
                    productName = "BritAma"
                }
                1000000 -> {
                    productName = "BritAma Bisnis"
                }
            }
            setStatusColorAndStatusBar(R.color.highlightColor, View.SYSTEM_UI_FLAG_VISIBLE);
            tvMinimalSetoran.text =
                "Setor minimum ${inquiry!!.minimumInitialDeposit.minimumString} untuk mulai bertransaksi dengan ${productName} kamu."
            officeName = inquiry!!.branch.name
            officeCode = inquiry!!.branch.code.toString()
            office = Office(
                "",
                officeName,
                "",
                officeCode,
                "",
                "",
            )

            tvKcpTerdaftar.text = office.name
            officeCode = office.branchCode
            saldoNominal = inquiry!!.minimumInitialDeposit.minimumFloat.toDouble()

            adapterRekomendasi =
                RekomendasiNewSkinAdapter(this@InquiryProductTabunganNewSkinActivity, inquiry!!.recomInitialDeposit, applicationContext)

            val layoutManager =
                LinearLayoutManager(applicationContext, LinearLayoutManager.HORIZONTAL, false)
            recyclerRecom.layoutManager = layoutManager
            recyclerRecom.itemAnimator = DefaultItemAnimator()
            recyclerRecom.adapter = adapterRekomendasi

            setListenerEdittext()
        }

    }

    open fun setListenerEdittext(){
        binding.apply {
            etSetoranAwal.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(s: Editable?) {
                    clearSelected()
                    if (!etSetoranAwal.text.isNullOrBlank()) {
                        saldoNominal = GeneralHelper.clearingAmountSigned("Rp" + etSetoranAwal.text.toString())
                            .toDouble()
                    }
                    try {
                        etSetoranAwal.removeTextChangedListener(this)
                        var value: String = etSetoranAwal.text.toString()
                        var str = value.replace(Constant.CURRENCY.toRegex(), "")
                            .replace("\\.".toRegex(), "")
                        str = getDecimalFormattedString(str)
                        value = str
                        etSetoranAwal.setText(value)
                        if (value.length > 0) {
                            Selection.setSelection(etSetoranAwal.text, value.length)
                        }
                        etSetoranAwal.addTextChangedListener(this)
                    } catch (ex: Exception) {
                        etSetoranAwal.addTextChangedListener(this)
                    }
                }

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    if (s?.length!! >= 1) {
                        if (getRawFloatFromFormatted(s.toString()) < (inquiry!!.minimumInitialDeposit.minimumFloat)) {
                            binding.rlEditNominal.background = resources.getDrawable(R.drawable.rounded_dialog_grey_newskin_off)
                            binding.tvSaldoNotice.visibility = View.VISIBLE
                            binding.tvSaldoNotice.text = "Setoran kurang dari batas minimum"
                            validasiButton(false)
                        } else if (isChecked) {
                            if (model.saldoReponse.balance < getRawFloatFromFormatted(s.toString())) {
                                    binding.rlEditNominal.background = resources.getDrawable(R.drawable.rounded_dialog_grey_newskin_off)
                                    binding.tvSaldoNotice.visibility = View.VISIBLE
                                    binding.tvSaldoNotice.text = "Saldo tidak cukup"
                            } else {
                                binding.rlEditNominal.background =
                                    resources.getDrawable(R.drawable.rounded_dialog_grey_newskin_on)
                                binding.tvSaldoNotice.visibility = View.GONE
                                validasiButton(true)
                            }
                        } else {
                            binding.rlEditNominal.background =
                                resources.getDrawable(R.drawable.rounded_dialog_grey_newskin_on)
                            binding.tvSaldoNotice.visibility = View.GONE
                            validasiButton(true)
                        }
                    } else {
                        validasiButton(false)
                        if (s.toString().equals("")) {
                            binding.ivClose.visibility = View.GONE
                        } else {
                            binding.ivClose.visibility = View.VISIBLE
                        }
                        binding.rlEditNominal.background = resources.getDrawable(R.drawable.rounded_dialog_grey_newskin)
                        binding.tvSaldoNotice.visibility = View.GONE

                    }
                }
            })
        }
    }

    private fun getDecimalFormattedString(value: String): String {
        return GeneralHelper.formatNominal(value)
    }

    fun getRawFloatFromFormatted(input: String): Float {
        val cleaned = input.replace(".", "").replace(",", ".")
        return try {
            cleaned.toFloat()
        } catch (e: Exception) {
            0f
        }
    }

    open fun setParameterModel(): ParameterPilihKantorModel? {
        var parameterModel = ParameterPilihKantorModel()
        parameterModel.type = product!!.type
        parameterModel.productType = product!!.productType
        parameterModel.urlOwnLocation = urlLokasiSendiri
        parameterModel.urlSearchLocation = urlLokasiPencarian
        parameterModel.urlInquiry = urlInquiry
        parameterModel.name = product!!.name

        return parameterModel
    }

    override fun onClick(p0: View?) {
        when(p0!!.id){

            R.id.btn_rekening_baru ->
                presenter1.getKonfirmasi(KonfirmasiTabunganRequest(model.acoount,saldoNominal.toString(),
                        product!!.productType, office.branchCode)
                )

            R.id.tv_kcp_terdaftar, R.id.iv_scroll -> {
                if (ContextCompat.checkSelfPermission(this, permissionLocation) != PackageManager.PERMISSION_GRANTED) {
                    if (ActivityCompat.shouldShowRequestPermissionRationale(this, permissionLocation)) {
                        ActivityCompat.requestPermissions(this, arrayOf(permissionLocation), 1)
                    } else {
                        showAlertPermission(getString(R.string.notes_need_permission))
                    }
                } else { showDialogLocation() }
            }

            R.id.iv_close -> onChangeAmount(binding.etSetoranAwal)

            R.id.tv_ganti, R.id.ivScrollAccount, R.id.ll_pilih_sumber_dana -> {
                counter++
                if (!binding.etSetoranAwal.text.toString().equals("")) {
                    val fragmentSumberDanaNew = SumberDanaFragmentNewSkin(
                        mListAccountModel,
                        counter,
                        GeneralHelper.clearingAmountSigned("Rp" + binding.etSetoranAwal.text.toString())
                            .toLong(),
                        this,
                        mListFailed,
                        isFromFastMenu
                    )
                    fragmentSumberDanaNew.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
                } else {
                    val fragmentSumberDanaNew = SumberDanaFragmentNewSkin(
                        mListAccountModel,
                        counter,
                        GeneralHelper.clearingAmountSigned("Rp" + 0).toLong(),
                        this,
                        mListFailed,
                        isFromFastMenu
                    )
                    fragmentSumberDanaNew.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
                }
            }
        }

    }

    private fun showDialogLocation() {
        val parameterModel = setParameterModel()
        if (parameterModel != null) {
            val bottomSheet = BottomSheetPilihKantorFragment.newInstance(parameterModel, office)

            bottomSheet.onOfficeSelectedListener = object : BottomSheetPilihKantorFragment.OnOfficeSelectedListener {
                override fun onOfficeSelected(officeSelected: Office) {
                    office = officeSelected
                    binding.tvKcpTerdaftar.text = office.name
                }
            }
            bottomSheet.show(supportFragmentManager, "BottomSheetPilihKantor")
        } else {
            Log.e("chooseBranch", "Parameter model is null")
        }
    }

    @SuppressLint("SetTextI18n")
    fun onChangeAmount(editText: EditText){
        clearSelected()
        editText.requestFocus()
        editText.setText("")
        binding.rlEditNominal.background = resources.getDrawable(R.drawable.rounded_dialog_grey_newskin)
        binding.tvSaldoNotice.visibility = View.GONE
        showKeyboard(this)
    }

    private fun showKeyboard(activity: Activity) {
        val view = activity.currentFocus
        val methodManager = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        assert(view != null)
        methodManager.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT)
    }

    override fun onSelectSumberDana(bankModel: AccountModel?) {
        if (bankModel != null) {
            isChecked = true
            model = bankModel
        }

        if (bankModel?.saldoReponse != null) {
            binding.tvSaldoRek.text = GeneralHelper.formatNominalIDR(
                    model.currency,
                    model.saldoReponse.balanceString
            )

            saldo = bankModel.saldoReponse.balance
            presenter1.getBranchCode(BranchRequest(model.acoount) )
        } else {
            binding.tvSaldoRek.text = String.format("%s%s", bankModel?.currency, "-")
            saldo = 0.0
        }

        binding.tvNoRek.text = model.acoountString

        if (bankModel?.imagePath != null) {
            if (!bankModel.imagePath.equals("", ignoreCase = true)) {
                GeneralHelper.loadImageUrl(
                        this,
                        bankModel.imagePath,
                    binding.ivIconRek,
                        R.drawable.bri,
                        0
                )
            } else {
                binding.ivIconRek.setImageResource(R.drawable.bri)
            }
        } else {
            binding.ivIconRek.setImageResource(R.drawable.bri)
        }
    }

    override fun onSendFailedList(list: List<Int>?) {
        mListFailed = list!!
    }


    override fun onSuccessKonfirmasi(generalConfirmationResponse: GeneralConfirmationResponse) {
        KonfirmasiTabunganRevNewSkinActivity.launchIntentBukaRekening(this, generalConfirmationResponse, model, msaldoString!!,
                mUrlPayment,GeneralHelper.getString(R.string.url_buka_tabungan_pending_revamp),false
        )
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_BUKA_REKENING && data != null) {
            if (resultCode == RESULT_OK) {
                val gson = Gson()
                office = gson.fromJson(data.getStringExtra("data_kantor"), Office::class.java)
                binding.tvKcpTerdaftar.text = office.name
            } else if (resultCode == RESULT_CANCELED && data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != Constant.TAG_ERROR_MESSAGE) {
                showSnackbarErrorMessage(
                        data.getStringExtra(Constant.TAG_ERROR_MESSAGE),
                        ALERT_ERROR,
                        this,
                        false
                )
            }
            else if (resultCode == RESULT_FIRST_USER){
                setResult(RESULT_FIRST_USER, data)
                finish()
            }
        }
        else if (resultCode == Constant.REQ_PETTUNJUK1) {
            fragmentDialogNoImageRevamp!!.dismiss()
        }

    }

    override fun onItemClick(position: Int) {
        binding.etSetoranAwal.setText(String.format("%.0f", inquiry!!.recomInitialDeposit.get(position).recomFloat.toFloat()))

        clearSelected()
        inquiry!!.recomInitialDeposit.get(position).isSelected = true
        adapterRekomendasi.notifyDataSetChanged()
    }

    fun clearSelected() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            inquiry!!.recomInitialDeposit.forEach(Consumer { p: RecomInitialDepositItem ->
                p.isSelected = false
            })
        } else {
            for (p in inquiry!!.recomInitialDeposit) {
                p.isSelected = false
            }
        }
        adapterRekomendasi.notifyDataSetChanged()
    }

    override fun onClickYes(type: String?) {
        PilihanKantorGeneralActivity.launchIntent(
                this,
                setParameterModel(),
                office
        )
    }

    override fun onClickToSafety() {
    }

    override fun onDestroy() {
        presenter1.stop()
        super.onDestroy()
    }

    override fun onRequestPermissionsResult(
            requestCode: Int,
            permissions: Array<out String>,
            grantResults: IntArray
    ) {
        when (requestCode) {
            1 -> {
                // If request is cancelled, the result arrays are empty.
                if (grantResults.size > 0
                        && grantResults[0] === PackageManager.PERMISSION_GRANTED
                ) {
                    fragmentDialogNoImageRevamp = FragmentDialogNoImageRevamp.newInstance(
                            this,
                            inquiry!!.chooseBranchContent.title,
                            inquiry!!.chooseBranchContent.description,
                            "Saya Mengerti",
                            Constant.OPEN_ACCOUNT_GENERAL,
                            true,
                            true
                    )
                    fragmentDialogNoImageRevamp!!.show(supportFragmentManager, "")
                }
                return
            }
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    fun setButton(boolean: Boolean){
        if (boolean){
            binding.btnRekeningBaru.isEnabled = true
            binding.btnRekeningBaru.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
            binding.btnRekeningBaru.background = ContextCompat.getDrawable(this, R.drawable.button_primary_bg)
        }else{
            binding.btnRekeningBaru.isEnabled = false
            binding.btnRekeningBaru.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
            binding.btnRekeningBaru.background = ContextCompat.getDrawable(this, R.drawable.rounded_button_disabled_revamp)
        }
    }
}