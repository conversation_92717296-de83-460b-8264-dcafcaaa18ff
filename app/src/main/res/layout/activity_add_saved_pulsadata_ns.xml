<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:context="id.co.bri.brimo.ui.activities.AddSavedPdamActivity">

        <id.co.bri.brimo.ui.widget.BaseScreenLayout
            android:id="@+id/bsl_content"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/ll_bottom">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Nomor Pelanggan input -->
                <id.co.bri.brimo.ui.widget.input_til.BaseInputView
                    android:id="@+id/biv_no_pelanggan"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:prefixText="+62"
                    app:expanded="false"
                    app:hintText="Nomor HP" />

                <!-- Nama Tersimpan input -->
                <id.co.bri.brimo.ui.widget.input_til.BaseInputView
                    android:id="@+id/etSavedName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="21dp"
                    app:hintText="Nama Tersimpan" />
            </LinearLayout>

        </id.co.bri.brimo.ui.widget.BaseScreenLayout>

        <!-- Bottom Button -->
        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:elevation="8dp"
            android:background="@android:color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <Button
                android:id="@+id/btnSubmit"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                style="@style/CustomButtonStyle"
                android:text="Simpan"
                android:textSize="16sp"
                android:textAllCaps="false"
                android:background="@drawable/rounded_button_ns"
                android:textColor="@color/selector_text_color_button_primary_ns"
                android:enabled="false" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>