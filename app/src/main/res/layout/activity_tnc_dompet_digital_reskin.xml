<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:tools="http://schemas.android.com/tools"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:background="@drawable/bg_new_skin_activity_container"
		tools:context="id.co.bri.brimo.ui.activities.dompetdigitalreskin.TncDompetDigitalReskinActivity">

	<!-- Toolbar -->
	<include
			android:id="@+id/toolbar"
			layout="@layout/toolbar_new_skin"
			android:layout_width="match_parent"
			android:layout_height="wrap_content" />

	<androidx.coordinatorlayout.widget.CoordinatorLayout
			android:id="@+id/content"
			android:layout_width="match_parent"
			android:layout_height="match_parent">

		<RelativeLayout
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:paddingTop="1dp"
				android:layout_marginTop="70dp"
				android:background="@drawable/bg_card_rounded_ns"
				>

		<ScrollView
				android:id="@+id/scrollview"
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:orientation="vertical">

			<LinearLayout
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:orientation="vertical"
					android:paddingTop="16dp">

				<LinearLayout
						android:layout_width="match_parent"
						android:layout_height="match_parent"
						android:orientation="vertical"
						android:paddingHorizontal="@dimen/size_16dp">

					<WebView
							android:id="@+id/wv_syarat"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							/>

				</LinearLayout>

						<View
								android:id="@+id/bottom_border"
								android:layout_width="wrap_content"
								android:layout_height="@dimen/size_1dp"
								android:background="@color/border_gray_soft_ns" />

					<LinearLayout
							android:id="@+id/btn_container"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:layout_gravity="bottom"
							android:orientation="vertical"
							android:paddingHorizontal="@dimen/size_16dp"
							android:background="@android:color/white">


						<Button
								android:id="@+id/bt_setuju"
								android:layout_width="match_parent"
								android:layout_height="56dp"
								android:layout_marginVertical="15dp"
								style="@style/CustomButtonStyle"
								android:background="@drawable/rounded_button_ns"
								android:text="@string/txt_saya_setuju"
								android:textAllCaps="false"
								android:textColor="@color/selector_text_color_button_primary_ns"
								android:textSize="16sp" />
					</LinearLayout>

			</LinearLayout>
		</ScrollView>
			<ImageView
					android:id="@+id/img_bawah"
					android:layout_width="32dp"
					android:layout_height="32dp"
					android:layout_marginBottom="@dimen/size_24dp"
					android:layout_alignParentBottom="true"
					android:layout_centerHorizontal="true"
					android:src="@drawable/ic_scroll_down_ns" />
		</RelativeLayout>

	</androidx.coordinatorlayout.widget.CoordinatorLayout>


</FrameLayout>