package id.co.bri.brimo.ui.activities.pulsadata.reskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HistoryAdapterNs
import id.co.bri.brimo.adapters.SavedAdapterNs
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.IFormListrikReskinPresenter
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ISavedListrikReskinView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataException
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaDataResult
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.PulsaErrorCode
import id.co.bri.brimo.databinding.ActivitySearchSavedHistoryBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.ProviderItem
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.listrikrevamp.reskin.FavoriteEvent
import id.co.bri.brimo.ui.fragments.UpdateSavedItemNsFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.HapusConfirmationBottomSheetFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.util.RxBus
import io.reactivex.android.schedulers.AndroidSchedulers
import javax.inject.Inject
import kotlin.collections.forEach

class SearchSavedHistoryPulsaDataActivity: NewSkinBaseActivity(), SavedAdapterNs.ClickItem, HistoryAdapterNs.ClickItem, IPulsaDataReskinView {
    private var _binding: ActivitySearchSavedHistoryBinding? = null
    protected val binding get() = _binding!!

    private lateinit var savedAdapter: SavedAdapterNs
    private lateinit var historyAdapter: HistoryAdapterNs
    private var filteredSavedResponses = mutableListOf<SavedResponse>()
    private var filteredHistoryResponses = mutableListOf<HistoryResponse>()
    private var defaultIcon = 0
    private var lastClickTime = 0L
    private var currentSearchQuery = ""
    val providerList: MutableList<ProviderItem> = mutableListOf()

    @Inject
    lateinit var presenter: IPulsaDataReskinPresenter<IPulsaDataReskinView>

    companion object Companion {
        private var savedResponse: MutableList<SavedResponse> = mutableListOf()
        private var historyResponse: MutableList<HistoryResponse> = mutableListOf()

        private var dataFormPulsa: FormPulsaDataResponse?= null

        @JvmStatic
        fun launchIntent(caller: Activity,
                         savedResponses: MutableList<SavedResponse>,
                         historyResponses: MutableList<HistoryResponse>,
                         dataForm: FormPulsaDataResponse,
                         fromFastMenu: Boolean) {
            dataFormPulsa = dataForm
            isFromFastMenu = fromFastMenu
            savedResponse = savedResponses
            historyResponse = historyResponses

            caller.apply {
                startActivityForResult(
                    Intent(
                        this,
                        SearchSavedHistoryPulsaDataActivity::class.java
                    ), Constant.REQ_PAYMENT)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivitySearchSavedHistoryBinding.inflate(layoutInflater)

        setContentView(binding.root)
        RxBus.listen(FavoriteEvent::class.java)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { event ->
                if(event.isEdit) {
                    showSnackbar("Nama berhasil diubah.", ALERT_CONFIRM)
                    showProgress()
                    presenter.executeRequest(
                        url = if(isFromFastMenu) GeneralHelper.getString(R.string.url_form_pulsa_data_v5)
                        else GeneralHelper.getString(R.string.url_form_pulsa_data_revamp),
                        requestParam = if(isFromFastMenu) presenter.getFastMenuRequest() else null,
                        responseType = FormPulsaDataResponse::class.java
                    )
                }
            }
        onBindIntentData()

        injectDependency()

        onBindView()
    }

    private fun onBindView() {
        GeneralHelperNewSkin.setToolbar(
            this,binding.toolbar.toolbar,
            "Pulsa & Paket Data",
        )

        providerList.addAll(dataFormPulsa!!.provider)

        // Initialize filtered lists as empty initially (blank search result)
        filteredSavedResponses.clear()
        filteredHistoryResponses.clear()

        updateEmptyState()
        setupAdapters()
        setupSearchView()
    }

    private fun setupAdapters() {
        // Saved adapter
        initiateSavedAdapter()

        // History adapter
        initiateHistoryAdapter()
    }

    fun initiateHistoryAdapter() {
        binding.rvRiwayat.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        historyAdapter =
            HistoryAdapterNs(
                this,
                historyResponse,
                this,
                0,
                isFromFastMenu,
                "pulsa",
            )
        binding.rvRiwayat.adapter = historyAdapter
    }

    fun initiateSavedAdapter() {
        binding.rvDaftarFavorit.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        savedAdapter =
            SavedAdapterNs(this, savedResponse, this, 0, BaseActivity.isFromFastMenu)
        binding.rvDaftarFavorit.adapter = savedAdapter
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        presenter.apply {
            view = this@SearchSavedHistoryPulsaDataActivity
            start()
        }
    }

    private fun setupSearchView() {
        // Request focus on SearchView when activity starts
        binding.searchView.post {
            binding.searchView.requestFocus()
        }

        // Handle focus change to manually update background
        binding.searchView.setOnQueryTextFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                binding.searchView.setBackgroundResource(R.drawable.bg_input_double_border_focused_ns)
            } else {
                binding.searchView.setBackgroundResource(R.drawable.bg_input_black_100_brimo_ns)
            }
        }

        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                currentSearchQuery = newText ?: ""
                filterData(currentSearchQuery)
                return false
            }
        })
    }

    private fun filterData(query: String) {
        val lowerCaseQuery = query.lowercase()

        // Filter saved responses
        filteredSavedResponses.clear()
        if (query.isNotEmpty()) {
            savedResponse.forEach { saved ->
                if (saved.title?.lowercase()?.contains(lowerCaseQuery) == true) {
                    filteredSavedResponses.add(saved)
                }
            }
        }

        // Filter history responses
        filteredHistoryResponses.clear()
        if (query.isNotEmpty()) {
            historyResponse.forEach { history ->
                if (history.title?.lowercase()?.contains(lowerCaseQuery) == true) {
                    filteredHistoryResponses.add(history)
                }
            }
        }

        // Update adapters with search query for highlighting
        savedAdapter.setSearchQuery(query)
        historyAdapter.setSearchQuery(query)

        // Notify adapters
        savedAdapter.notifyDataSetChanged()
        historyAdapter.notifyDataSetChanged()

        // Update empty state
        updateEmptyState(query.isNotEmpty())
    }

    private fun updateEmptyState(isSearching: Boolean = false) {
        // Show/hide sections based on content and search state
        binding.contentFavorit.visibility = if (filteredSavedResponses.isNotEmpty()) View.VISIBLE else View.GONE
        binding.contentRiwayat.visibility = if (filteredHistoryResponses.isNotEmpty()) View.VISIBLE else View.GONE

        // Show/hide start search view based on whether user is searching
        if (isSearching) {
            // User has entered some characters, hide start search view
            binding.llStartSearch.visibility = View.GONE

            // Show no search results if searching and both lists are empty
            val hasAnyResults = filteredSavedResponses.isNotEmpty() || filteredHistoryResponses.isNotEmpty()
            if (!hasAnyResults) {
                binding.llNoDataSearchFound.visibility = View.VISIBLE
            } else {
                binding.llNoDataSearchFound.visibility = View.GONE
            }
        } else {
            // No query entered, show start search view and hide no data found
            binding.llStartSearch.visibility = View.VISIBLE
            binding.llNoDataSearchFound.visibility = View.GONE
        }
    }

    private fun updateSavedItemInLists(data: SavedResponse, position: Int, updateAction: (SavedResponse) -> Unit) {
        // Find and update item in original list
        val originalIndex = savedResponse.indexOfFirst { it.value == data.value }
        if (originalIndex != -1) {
            updateAction(savedResponse[originalIndex])
        }

        // Find and update item in filtered list
        val filteredIndex = filteredSavedResponses.indexOfFirst { it.value == data.value }
        if (filteredIndex != -1) {
            updateAction(filteredSavedResponses[filteredIndex])
            savedAdapter.notifyItemChanged(filteredIndex)
        }
    }

    fun findProviderByPrefix(phone: String): ProviderItem? {
        val cleanedPhone = phone.replace(Regex("[^0-9]"), "")
        val prefix4 = cleanedPhone.take(4)
        val prefix5 = if (cleanedPhone.length >= 5) cleanedPhone.take(5) else ""

        return providerList.firstOrNull { provider ->
            (prefix5.isNotEmpty() && GeneralHelper.isContains(provider.prefix, prefix5)) ||
                    GeneralHelper.isContains(provider.prefix, prefix4)
        }
    }

    fun normalizePhoneNumber(input: String): String {
        return input.replace(Regex("^((\\+62)|62|0)"), "")
    }

    private fun handlingError(code: String) {
        println("handlingError: $code")
        when (code) {
            PulsaErrorCode.EXCEPTION_93.code -> {
                showGeneralErrorDialog()
            }
            PulsaErrorCode.EXCEPTION_12.code -> {
                showGeneralErrorDialog {
                    presenter.executeRequest(
                        url = if(isFromFastMenu) GeneralHelper.getString(R.string.url_form_pulsa_data_v5)
                        else GeneralHelper.getString(R.string.url_form_pulsa_data_revamp),
                        requestParam = if(isFromFastMenu) presenter.getFastMenuRequest() else null,
                        responseType = FormPulsaDataResponse::class.java
                    )
                }
//                showSnackbar("Terjadi gangguan sistem. Silakan coba lagi, ya.", ALERT_ERROR)
            }
        }
    }

    private fun showGeneralErrorDialog(
        title: String = "Terjadi Kesalahan",
        message: String = "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
        imgName: String = "ic_sad_illustration",
        onRetry: (() -> Unit)? = null
    ) {
        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_sad_illustration,
            imgName,
            title,
            message,
            btnFirstFunction = { onRetry?.invoke() },
            btnSecondFunction = { /* optional */ },
            false,
            firstBtnTxt = getString(R.string.retry),
            secondBtnTxt = getString(R.string.close),
            false,
            showCloseButton = true,
            showPill = true
        )
    }

    private fun onBindIntentData() {
    }

    override fun onClickSavedItem(data: SavedResponse?) {
        finish()
        val number = data?.subtitle!!.replace(Regex("[^0-9]"), "")

        findProviderByPrefix(number)?.let {
            InquiryPulsaDataReskinActivity.launchIntent(this, isFromFastMenu, ReqInquiry(
                normalizePhoneNumber(number),
                it, dataFormPulsa!!.referenceNumber,
                if(isFromFastMenu) dataFormPulsa!!.accountModel else mutableListOf()
            ),savedResponse)
        }
    }

    override fun onClickUpdateItem(
        savedResponse: SavedResponse?,
        position: Int
    ) {
        val updateSavedItemFragment = UpdateSavedItemNsFragment(savedResponse, {savedResponseItem, type, position ->
            when(type) {
                Constant.EditOptionNs.FAV -> {
                    val savedId = savedResponseItem?.value!!.split("|")[0]
                    showProgress()

                    presenter.executeRequest(
                        url = GeneralHelper.getString(R.string.url_favorite_pulsa),
                        requestParam = SavedListNs(
                            savedId = savedId.toInt()
                        ),
                        responseType = RestResponse::class.java,
                        isSaved = FavoriteType.favorite
                    )
                }
                Constant.EditOptionNs.EDIT -> {
                    savedResponseItem?.let { saved ->
                        val number = saved.subtitle?: ""
                        val savedId = saved.value!!.split("|")[0]

                        findProviderByPrefix(number)?.let {
                            AddSavedPulsaDataActivity.launchIntent(this, ReqInquiry(
                                normalizePhoneNumber(number),
                                it, dataFormPulsa!!.referenceNumber,
                                mutableListOf(), saved.title, savedId
                            ), isFromFastMenu)
                        }
                    }
                }
                Constant.EditOptionNs.HAPUS -> {
                    showProgress()
                    val bottomSheetFragment = HapusConfirmationBottomSheetFragment.newInstance(
                        savedResponseItem = savedResponseItem,
                        onConfirm = {
                            val savedId = savedResponseItem?.value!!.split("|")[0]

                            presenter.executeRequest(
                                url = GeneralHelper.getString(R.string.url_delete_pulsa),
                                requestParam = SavedListNs(
                                    savedId = savedId.toInt()
                                ),
                                responseType = RestResponse::class.java,
                                isSaved = FavoriteType.removeFavorite
                            )
                        },
                        onCancel = {
                            // Just dismiss the bottom sheet, no action needed
                        }
                    )
                    bottomSheetFragment.show(supportFragmentManager, "HapusConfirmationBottomSheet")
                }
                Constant.EditOptionNs.NON_FAV -> {
                    showProgress()
                    val savedId = savedResponseItem?.value!!.split("|")[0]

                    presenter.executeRequest(
                        url = GeneralHelper.getString(R.string.url_unfavorite_pulsa),
                        requestParam = SavedListNs(
                            savedId = savedId.toInt()
                        ),
                        responseType = RestResponse::class.java,
                        isSaved = FavoriteType.unfavorite
                    )
                }
            }
        }, position)
        updateSavedItemFragment.show(supportFragmentManager, "")
    }

    override fun onClickHistoryItem(historyResponse: HistoryResponse?) {
        val number = historyResponse?.subtitle!!.replace(Regex("[^0-9]"), "")

        findProviderByPrefix(number)?.let {
            InquiryPulsaDataReskinActivity.launchIntent(this, isFromFastMenu, ReqInquiry(
                normalizePhoneNumber(number),
                it, dataFormPulsa!!.referenceNumber,
                if(isFromFastMenu) dataFormPulsa!!.accountModel else mutableListOf()
            ),savedResponse.toMutableList())
        }
    }

    override fun onSuccess(res: PulsaDataResult) {
        when (res) {
            is PulsaDataResult.Form -> {
                hideProgress()
                val formPulsaDataResponse = res.data

                historyResponse.apply {
                    clear()
                    addAll(formPulsaDataResponse.history)
                }
                savedResponse.apply {
                    clear()
                    addAll(formPulsaDataResponse.saved)
                }

                dataFormPulsa = formPulsaDataResponse
                providerList.addAll(dataFormPulsa!!.provider)

                savedAdapter.notifyDataSetChanged()
                historyAdapter.notifyDataSetChanged()
            }
            is PulsaDataResult.Favorite -> {
                val type = res.data

                presenter.executeRequest(
                    url = if(isFromFastMenu) GeneralHelper.getString(R.string.url_form_pulsa_data_v5)
                    else GeneralHelper.getString(R.string.url_form_pulsa_data_revamp),
                    requestParam = if(isFromFastMenu) presenter.getFastMenuRequest() else null,
                    responseType = FormPulsaDataResponse::class.java
                )

                showSnackbar(when (type) {
                    FavoriteType.favorite -> "Daftar berhasil di Pin."
                    FavoriteType.removeFavorite -> "Daftar Favorit berhasil dihapus."
                    FavoriteType.unfavorite -> "Daftar Favorit berhasil diunpin."
                    else -> ""
                }, ALERT_CONFIRM)
            }
            else -> Unit
        }
    }

    override fun onExceptionReskin(exception: PulsaDataException) {
        when (exception) {
            is PulsaDataException.KnownError -> handlingError(exception.code)
            is PulsaDataException.UnknownError -> {
                OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation(
                    supportFragmentManager,
                    R.drawable.ic_sad_illustration,
                    "ic_sad_illustration",
                    "Terjadi Kesalahan",
                    "Kami mengalami kendala saat memuat data. Silakan coba beberapa saat lagi, ya.",
                    btnFirstFunction = {

                    },
                    btnSecondFunction = {

                    },
                    false,
                    firstBtnTxt = "Coba Lagi",
                    secondBtnTxt = "Tutup",
                    false,
                    showCloseButton = true,
                    showPill = true
                )
            }
        }
    }

    override fun onSuccessAccountList(
        accountList: MutableList<AccountModel>,
        mainAccount: AccountModel
    ) {
    }

    override fun onDestroy() {
        super.onDestroy()
        isFromFastMenu = false
    }
}