package id.co.bri.brimo.payment.feature.qrtap.data.impl

import id.co.bri.brimo.payment.core.network.processApi
import id.co.bri.brimo.payment.core.network.request.base.SaldoNormalRequest
import id.co.bri.brimo.payment.core.network.request.qrtap.QrTapPayloadRequest
import id.co.bri.brimo.payment.core.network.response.base.SaldoNormalResponse
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapCheckStatusResponse
import id.co.bri.brimo.payment.core.network.response.qrtap.QrTapPayloadResponse
import id.co.bri.brimo.payment.dependency.PaymentDependency
import id.co.bri.brimo.payment.feature.qrtap.data.api.QrTapRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

internal class QrTapRepositoryImpl(
    private val ioDispatcher: CoroutineDispatcher
) : QrTapRepository {

    override suspend fun postQrTapPayload(
        request: QrTapPayloadRequest,
        fastMenu: Boolean
    ): QrTapPayloadResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc="
            } else {
                "ziM9XOo3ZgCnFZy7basZtsn/Zs1b6hYUfCKZFfiOiIE="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = fastMenu
                )
            }
        }

    override suspend fun postQrTapCheckStatus(
        fastMenu: Boolean
    ): QrTapCheckStatusResponse =
        withContext(ioDispatcher) {
            val url = if (fastMenu) {
                "ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k="
            } else {
                "ikqrtshHLBm3ZDmfO6ifLYVf05scbL66EEWiUPd/+VY="
            }
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = "",
                    fastMenu = fastMenu
                )
            }
        }

    override suspend fun postSaldoNormal(
        request: SaldoNormalRequest
    ): SaldoNormalResponse =
        withContext(ioDispatcher) {
            val url = "gnUBRK3Mc0ogiZdhmA6hBg=="
            processApi {
                PaymentDependency.getPaymentApi()?.hitApi(
                    url = url,
                    request = request,
                    fastMenu = false
                )
            }
        }
}
