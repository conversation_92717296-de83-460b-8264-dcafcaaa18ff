/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <fbjni/fbjni.h>

namespace facebook {
namespace jni {

class JBuffer : public JavaClass<JBuffer> {
 public:
  static constexpr const char* kJavaDescriptor = "Ljava/nio/Buffer;";

  void rewind() const;
  bool isDirect() const;
  void* getDirectAddress() const;
  size_t getDirectCapacity() const;
};

class JByteOrder : public JavaClass<JByteOrder> {
 public:
  constexpr static const char* kJavaDescriptor = "Ljava/nio/ByteOrder;";

  static local_ref<JByteOrder> nativeOrder();
};

// JNI's NIO support has some awkward preconditions and error reporting. This
// class provides much more user-friendly access.
class JByteBuffer : public JavaClass<JByteBuffer, JBuffer> {
 public:
  static constexpr const char* kJavaDescriptor = "Ljava/nio/ByteBuffer;";

  static local_ref<JByteBuffer> wrapBytes(uint8_t* data, size_t size);
  static local_ref<JByteBuffer> allocateDirect(jint size);

  local_ref<JByteBuffer> order(alias_ref<JByteOrder>);

  uint8_t* getDirectBytes() const {
    return static_cast<uint8_t*>(getDirectAddress());
  }

  size_t getDirectSize() const {
    return getDirectCapacity();
  }
};

} // namespace jni
} // namespace facebook
