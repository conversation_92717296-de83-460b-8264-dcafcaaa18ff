package id.co.bri.brimo.payment

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.nfc.Tag
import android.nfc.tech.IsoDep
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import id.co.bri.brimo.data.api.ApiService
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.rx.AppSchedulerProvider
import id.co.bri.brimo.nfcpayment.NFCPayment
import id.co.bri.brimo.nfcpayment.NFCPaymentService
import id.co.bri.brimo.payment.core.network.response.BrizziCheckBalanceResponse
import id.co.bri.brimo.payment.core.network.response.BrizziConfirmationResponse
import id.co.bri.brimo.payment.dependency.PaymentApi
import id.co.bri.brimo.payment.feature.brizzi.data.model.CardModel
import id.co.bri.brimo.ui.activities.CekBrizziDuaActivity
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.TapBrizziAktivasiActivity
import id.co.bri.brimo.ui.activities.dompetdigitalreskin.FormDompetDigitalReskinActivity
import id.co.bri.brizzi.Brizzi
import id.co.bri.brizzi.CardData
import id.co.bri.brizzi.callbacks.BrizziCallback
import id.co.bri.brizzi.exception.BrizziException
import io.reactivex.disposables.CompositeDisposable
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.first
import java.util.concurrent.TimeUnit

class PaymentImpl(
    private val apiService: ApiService,
    private val brimoPrefSource: BRImoPrefSource,
    private val gson: Gson,
    private val apiSource: ApiSource
) : PaymentApi {

    val schedulerProvider = AppSchedulerProvider()

    override suspend fun hitApi(url: String, request: Any, fastMenu: Boolean): String {
        val compositeDisposable = CompositeDisposable()
        val seqNum = brimoPrefSource.seqNumber
        val requestCheck = if (request is String && request == "") {
            JsonObject().apply {
                addProperty("username", "")
            }
        } else {
            request
        }
        val requestString = gson.toJson(requestCheck)
        val requestUpdated = if (fastMenu) {
            val jsonElement = JsonParser.parseString(requestString)
            val jsonObject = jsonElement.asJsonObject
            jsonObject.addProperty("username", brimoPrefSource.username)
            jsonObject.addProperty("token_key", brimoPrefSource.tokenKey)
            jsonObject.toString()
        } else {
            requestString
        }

        return callbackFlow {
            compositeDisposable.add(
                apiSource.getDataString(url, requestUpdated, seqNum)
                    .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                    .subscribeOn(schedulerProvider.io())
                    .observeOn(schedulerProvider.mainThread())
                    .subscribe(
                        { response ->
                            val restResponse = MapperHelper.stringToRestResponse(response, seqNum)
                            val stringResponse = gson.toJson(restResponse)
                            trySend(stringResponse)
                        },
                        { error ->
                            close(error)
                        }
                    )
            )

            awaitClose {
                compositeDisposable.dispose()
            }
        }.first()
    }

    private lateinit var brizzi: Brizzi

    override fun initCheckBalance(
        tag: Tag,
        onSuccess: (String, String) -> Unit,
        onError: (Throwable) -> Unit
    ) {
        val brizziTag = IsoDep.get(tag)
        brizzi = Brizzi(brizziTag)

        brizzi.initCheckbalance(object : BrizziCallback {
            override fun onSuccess(cardData: CardData) {
                onSuccess(cardData.cardNumber, cardData.randomSAM)
            }

            override fun onFailure(error: BrizziException) {
                onError(Exception(error.errorCode, Exception(error.message)))
            }
        })
    }

    override fun commitCheckBalance(
        response: BrizziCheckBalanceResponse,
        onSuccess: (BrizziCheckBalanceResponse, CardModel) -> Unit,
        onError: (Throwable) -> Unit
    ) {
        brizzi.commitCheckBalance(response.key + response.rcHost, object : BrizziCallback {
            override fun onSuccess(cardData: CardData) {
                val cardModel = CardModel(
                    cardNumber = cardData.cardNumber,
                    cardBalance = cardData.cardBalance,
                    cardStatus = cardData.cardStatus,
                    validateRandom = cardData.validateRandom,
                    cardHistory = cardData.cardHistory.map {
                        CardModel.History(
                            type = it.trxType,
                            date = it.trxDate,
                            time = it.trxTime,
                            amount = it.amount
                        )
                    }
                )
                onSuccess(response, cardModel)
            }

            override fun onFailure(error: BrizziException) {
                onError(Exception(error.errorCode, Exception(error.message)))
            }
        })
    }

    override fun scanPayment(
        launcher: ActivityResultLauncher<Intent>,
        activity: ComponentActivity,
        confirmationResponse: BrizziConfirmationResponse,
        pin: String,
        fastMenu: Boolean
    ) {
        val response = Gson().toJson(confirmationResponse)
        CekBrizziDuaActivity.launchIntent(
            launcher,
            activity,
            response,
            pin,
            fastMenu
        )
    }

    override fun activatePayment(
        launcher: ActivityResultLauncher<Intent>,
        activity: ComponentActivity,
        fastMenu: Boolean
    ) {
        TapBrizziAktivasiActivity.launchIntent(
            launcher,
            activity,
            fastMenu
        )
    }

    override fun onPin(activity: ComponentActivity) {
        LupaPinActivity.launchIntent(activity)
    }

    override fun onSession(activity: ComponentActivity) {
        FastMenuNewSkinActivity.launchIntentSessionEnd(activity, "")
    }

    override fun navigateToWallet(
        activity: ComponentActivity,
        fastMenu: Boolean
    ) {
        FormDompetDigitalReskinActivity.launchIntent(activity, fastMenu)
    }

    override fun navigateToBpjs(
        activity: ComponentActivity,
        fastMenu: Boolean
    ) {
        // FormBpjsActivity.launchIntent(activity, fastMenu)
    }

    override fun navigateToTravel(
        activity: ComponentActivity,
        fastMenu: Boolean
    ) {
        // TravelMenuActivity.launchIntent(activity)
    }

    private var nfcPayment: NFCPayment? = null

    override fun onStartPayment(
        activity: ComponentActivity,
        payload: String
    ) {
        NFCPaymentService.start(activity, payload)
    }

    override fun onInitPayment(
        context: Context,
        activity: ComponentActivity,
        nfcServiceReceiver: BroadcastReceiver
    ) {
        LocalBroadcastManager.getInstance(context).registerReceiver(
            nfcServiceReceiver,
            IntentFilter(NFCPaymentService.ACTION_NFC)
        )

        nfcPayment = NFCPayment.getInstance(context)
        nfcPayment?.setActivityStatus(true)
        nfcPayment?.disableReaderMode(activity)
    }

    override fun onDestroyPayment(
        context: Context,
        nfcServiceReceiver: BroadcastReceiver
    ) {
        nfcPayment?.setActivityStatus(false)

        LocalBroadcastManager.getInstance(context).unregisterReceiver(nfcServiceReceiver)
    }
}
