<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Bagian Token Listrik -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:paddingHorizontal="21dp"
            android:background="@drawable/bg_input_black_100_brimo_ns">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="vertical"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/shimmer_btn_token_check"
                app:layout_constraintBottom_toBottomOf="parent">

                <View
                    android:layout_width="120dp"
                    android:layout_height="14dp"
                    android:background="@drawable/bg_skeleton_rounded_reskin" />

                <View
                    android:layout_width="180dp"
                    android:layout_height="12dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/bg_skeleton_rounded_reskin" />
            </LinearLayout>

            <View
                android:id="@+id/shimmer_btn_token_check"
                android:layout_width="103dp"
                android:layout_height="32dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:background="@drawable/bg_skeleton_rounded_reskin" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Bagian Jenis Listrik -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:layout_marginTop="24dp"
            android:paddingHorizontal="21dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:background="@drawable/bg_input_black_100_brimo_ns">

            <View
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/bg_skeleton_circle_reskin" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <View
                    android:layout_width="100dp"
                    android:layout_height="14dp"
                    android:background="@drawable/bg_skeleton_rounded_reskin" />
                <View
                    android:layout_width="150dp"
                    android:layout_height="14dp"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/bg_skeleton_rounded_reskin" />
            </LinearLayout>
        </LinearLayout>

        <!-- Nomor Pelanggan -->
        <View
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/bg_skeleton_rounded_reskin" />

        <!-- Search bar -->
        <View
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/bg_skeleton_rounded_reskin" />

        <!-- Tab Favorit & Riwayat -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp">

            <View
                android:layout_width="71dp"
                android:layout_height="32dp"
                android:background="@drawable/bg_skeleton_rounded_reskin" />

            <View
                android:layout_width="71dp"
                android:layout_height="32dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/bg_skeleton_rounded_reskin" />
        </LinearLayout>

        <!-- List Item Placeholder -->
        <include
            layout="@layout/skeleton_list_saved_form_reskin"/>
        <include
            layout="@layout/skeleton_list_saved_form_reskin"/>
        <include
            layout="@layout/skeleton_list_saved_form_reskin"/>

    </LinearLayout>
</layout>