<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:orientation="horizontal">

        <View
            android:id="@+id/v_circle_list"
            android:layout_width="32dp"
            android:layout_height="32dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@drawable/bg_skeleton_circle_reskin" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/v_circle_list"
            app:layout_constraintRight_toLeftOf="@+id/ll_circle_right_list"
            app:layout_constraintBottom_toBottomOf="parent"
            android:orientation="vertical"
            android:layout_weight="1">
            <View
                android:layout_width="154dp"
                android:layout_height="14dp"
                android:background="@drawable/bg_skeleton_rounded_reskin" />
            <View
                android:layout_width="223dp"
                android:layout_height="14dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/bg_skeleton_rounded_reskin" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_circle_right_list"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:orientation="horizontal"
            android:layout_weight="2">

            <View
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="14dp"
                android:background="@drawable/bg_skeleton_circle_reskin" />

            <View
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/bg_skeleton_circle_reskin" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
<!--<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    android:layout_width="match_parent"-->
<!--    android:layout_height="match_parent">-->

<!--</androidx.constraintlayout.widget.ConstraintLayout>-->