package id.co.bri.brimo.ui.widget

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.LayoutBaseScreenBinding
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin

class BaseScreenLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    val binding: LayoutBaseScreenBinding

    init {
        orientation = VERTICAL

        val inflater = LayoutInflater.from(context)
        binding = LayoutBaseScreenBinding.inflate(inflater, this, true)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()

        val childrenToMove = mutableListOf<View>()
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            if (child.id != R.id.ll_content && child.id != R.id.main_content) {
                childrenToMove.add(child)
            }
        }

        childrenToMove.forEach {
            removeView(it)
            binding.llContent.addView(it)
        }

        reMeasure()
    }

    fun setTextToolbar(activity: Activity, title: String) {
        GeneralHelperNewSkin.setToolbar(activity, binding.inclToolbar.toolbar, title)
    }

    private fun reMeasure() {
        requestLayout()
        invalidate()
    }
}