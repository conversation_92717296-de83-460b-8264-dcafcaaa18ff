package id.co.bri.brimo.payment.dependency

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.nfc.Tag
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import id.co.bri.brimo.payment.core.network.response.BrizziCheckBalanceResponse
import id.co.bri.brimo.payment.core.network.response.BrizziConfirmationResponse
import id.co.bri.brimo.payment.feature.brizzi.data.model.CardModel

interface PaymentApi {

    suspend fun hitApi(url: String, request: Any, fastMenu: Boolean): String

    fun initCheckBalance(
        tag: Tag,
        onSuccess: (String, String) -> Unit,
        onError: (Throwable) -> Unit
    )

    fun commitCheckBalance(
        response: BrizziCheckBalanceResponse,
        onSuccess: (BrizziCheckBalanceResponse, CardModel) -> Unit,
        onError: (Throwable) -> Unit
    )

    fun scanPayment(
        launcher: ActivityResultLauncher<Intent>,
        activity: ComponentActivity,
        confirmationResponse: BrizziConfirmationResponse,
        pin: String,
        fastMenu: Boolean
    )

    fun activatePayment(
        launcher: ActivityResultLauncher<Intent>,
        activity: ComponentActivity,
        fastMenu: Boolean
    )

    fun onPin(activity: ComponentActivity)

    fun onSession(activity: ComponentActivity)

    fun navigateToWallet(activity: ComponentActivity, fastMenu: Boolean)

    fun navigateToBpjs(activity: ComponentActivity, fastMenu: Boolean)

    fun navigateToTravel(activity: ComponentActivity, fastMenu: Boolean)

    fun onStartPayment(
        activity: ComponentActivity,
        payload: String
    )

    fun onInitPayment(
        context: Context,
        activity: ComponentActivity,
        nfcServiceReceiver: BroadcastReceiver
    )

    fun onDestroyPayment(
        context: Context,
        nfcServiceReceiver: BroadcastReceiver
    )
}
