package id.co.bri.brimo.adapters.listrik

import android.view.LayoutInflater
import android.view.ViewGroup
import id.co.bri.brimo.adapters.base.BaseItemViewHolder
import id.co.bri.brimo.adapters.base.BaseRecyclerViewAdapter
import id.co.bri.brimo.databinding.ItemListRiwayatListrikReskinBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.InboxResponse.ActivityList

class RiwayatAdapter: BaseRecyclerViewAdapter<ActivityList,
        RiwayatAdapter.RiwayatViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RiwayatViewHolder {
        context = parent.context
        val binding = ItemListRiwayatListrikReskinBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return RiwayatViewHolder(binding)
    }

    inner class RiwayatViewHolder(
        private val binding: ItemListRiwayatListrikReskinBinding
    ) : BaseItemViewHolder<ActivityList>(binding.root) {
        override fun bind(item: ActivityList) {
            val lines = item.subtitle.split("\n")
            val sSubtitle = lines[0]
            val sNoPref = lines[1]

            binding.tvDatetime.text = item.date
            binding.tvTitle.text = item.title
            binding.tvSubtitle.text = sSubtitle
            binding.tvNoref.text = sNoPref

            GeneralHelper.loadIconTransaction(
                context,
                item.iconPath,
                "",
                binding.ivIcon,
                GeneralHelper.getImageId(context, "ic_pln_polos"))

            binding.root.setOnClickListener {
                onItemClickListener?.onItemClick(it, item)
            }
        }
    }
}